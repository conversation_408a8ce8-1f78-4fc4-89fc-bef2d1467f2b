# Design Document

## Overview

The A-share Sector Analysis System is designed to provide comprehensive analysis of sector performance data spanning from 2020 to 2025. The system will analyze 86 sectors across all trading days, enabling users to identify top-performing sectors during specific time periods and track their daily ranking performance. The architecture follows a modular design with clear separation between data processing, analysis logic, and presentation layers.

## Architecture

### System Architecture

```mermaid
graph TB
    A[Web Frontend] --> B[FastAPI Backend]
    B --> C[Analysis Engine]
    B --> D[Data Loader]
    D --> E[CSV Data Files]
    C --> F[Ranking Calculator]
    C --> G[Period Analyzer]
    C --> H[Statistics Generator]
    B --> I[Caching Layer]
    
    subgraph "Data Layer"
        E
        J[sector_codes.json]
    end
    
    subgraph "Business Logic"
        C
        F
        G
        H
    end
    
    subgraph "API Layer"
        B
        I
    end
    
    subgraph "Presentation"
        A
    end
```

### Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant E as Analysis Engine
    participant D as Data Loader
    participant C as CSV Files
    
    U->>F: Select time period
    F->>A: POST /api/analyze
    A->>E: analyze_period(start_date, end_date)
    E->>D: load_period_data(start_date, end_date)
    D->>C: Read CSV files
    C-->>D: Raw sector data
    D-->>E: Processed data
    E->>E: Calculate rankings
    E-->>A: Analysis results
    A-->>F: JSON response
    F-->>U: Display results
```

## Components and Interfaces

### 1. Data Loader Component

**Purpose:** Efficiently load and process CSV data files for the specified time periods.

**Key Classes:**
- `SectorDataLoader`: Main data loading class
- `DateRangeProcessor`: Handles date range calculations across years
- `DataValidator`: Validates data integrity

**Interface:**
```python
class SectorDataLoader:
    def load_period_data(self, start_month: int, start_day: int, 
                        end_month: int, end_day: int, 
                        years: List[int]) -> Dict[int, pd.DataFrame]
    
    def get_available_years(self) -> List[int]
    
    def validate_date_range(self, start_month: int, start_day: int,
                           end_month: int, end_day: int) -> bool
```

### 2. Analysis Engine Component

**Purpose:** Core business logic for calculating sector performance metrics and rankings.

**Key Classes:**
- `SectorAnalyzer`: Main analysis orchestrator
- `RankingCalculator`: Calculates various ranking metrics
- `PerformanceMetrics`: Computes cumulative returns and statistics

**Interface:**
```python
class SectorAnalyzer:
    def analyze_period(self, start_month: int, start_day: int,
                      end_month: int, end_day: int) -> AnalysisResult
    
    def calculate_cumulative_returns(self, data: Dict[int, pd.DataFrame]) -> Dict[int, Dict[str, float]]
    
    def calculate_daily_top10_frequency(self, data: Dict[int, pd.DataFrame]) -> List[SectorFrequency]
    
    def calculate_daily_rank1_frequency(self, data: Dict[int, pd.DataFrame]) -> List[SectorFrequency]
```

### 3. API Layer Component

**Purpose:** RESTful API endpoints for frontend communication.

**Key Endpoints:**
- `POST /api/analyze`: Main analysis endpoint
- `GET /api/sectors`: Get available sectors
- `GET /api/years`: Get available years
- `GET /api/presets`: Get preset time periods

**Interface:**
```python
@app.post("/api/analyze")
async def analyze_sectors(request: AnalysisRequest) -> AnalysisResponse

@app.get("/api/sectors")
async def get_sectors() -> List[SectorInfo]

@app.get("/api/years")
async def get_available_years() -> List[int]
```

### 4. Frontend Component

**Purpose:** Interactive web interface for user interaction and result visualization.

**Key Features:**
- Date range selector
- Results display tables
- Sort controls
- Loading indicators

## Data Models

### Core Data Structures

```python
@dataclass
class SectorInfo:
    code: str
    name: str

@dataclass
class AnalysisRequest:
    start_month: int
    start_day: int
    end_month: int
    end_day: int
    sort_order: str = "desc"  # "asc" or "desc"

@dataclass
class SectorPerformance:
    sector_code: str
    sector_name: str
    cumulative_return: float
    year: int

@dataclass
class SectorFrequency:
    sector_code: str
    sector_name: str
    frequency_count: int
    total_days: int
    percentage: float

@dataclass
class AnalysisResult:
    cumulative_returns: Dict[int, List[SectorPerformance]]  # year -> top 10 sectors
    daily_top10_frequency: List[SectorFrequency]  # top 10 most frequent in daily top 10
    daily_rank1_frequency: List[SectorFrequency]  # top 10 most frequent rank 1
    period_info: Dict[str, Any]  # metadata about the analysis period
```

### Database Schema

The system uses CSV files as the primary data source. Each CSV file represents one trading day and contains:

```
sector_code,sector_name,date,open,close,high,low,volume,amount,amplitude,change_pct,change_amount,turnover_rate
```

Key fields for analysis:
- `sector_code`: Unique identifier for the sector
- `sector_name`: Human-readable sector name
- `date`: Trading date (YYYY-MM-DD format)
- `change_pct`: Daily percentage change (primary metric for rankings)

## Error Handling

### Error Types and Handling Strategy

1. **Data Loading Errors**
   - Missing CSV files: Log warning, continue with available data
   - Corrupted data: Skip invalid records, log errors
   - Date range validation: Return user-friendly error messages

2. **Analysis Errors**
   - Insufficient data: Return appropriate message to user
   - Calculation errors: Log error, return partial results if possible

3. **API Errors**
   - Invalid request parameters: Return 400 with validation details
   - Server errors: Return 500 with generic error message
   - Timeout errors: Return 504 with retry suggestion

### Error Response Format

```python
@dataclass
class ErrorResponse:
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: str
```

## Testing Strategy

### Unit Testing

1. **Data Loader Tests**
   - Test CSV file parsing
   - Test date range calculations
   - Test data validation logic

2. **Analysis Engine Tests**
   - Test ranking calculations with known data
   - Test cumulative return calculations
   - Test frequency counting logic

3. **API Tests**
   - Test endpoint responses
   - Test request validation
   - Test error handling

### Integration Testing

1. **End-to-End Workflow Tests**
   - Test complete analysis workflow
   - Test with various date ranges
   - Test with missing data scenarios

2. **Performance Tests**
   - Test with full dataset (5 years of data)
   - Measure response times
   - Test concurrent requests

### Test Data Strategy

- Use subset of real data for testing
- Create synthetic data for edge cases
- Mock external dependencies where appropriate

## Performance Considerations

### Optimization Strategies

1. **Data Loading Optimization**
   - Cache frequently accessed data
   - Use pandas for efficient data processing
   - Implement lazy loading for large datasets

2. **Analysis Optimization**
   - Pre-calculate common metrics
   - Use vectorized operations
   - Implement result caching

3. **API Optimization**
   - Implement response caching
   - Use async/await for I/O operations
   - Add request rate limiting

### Scalability Considerations

1. **Memory Management**
   - Process data in chunks for large datasets
   - Implement data cleanup after processing
   - Monitor memory usage

2. **Concurrent Processing**
   - Use thread pools for parallel data loading
   - Implement request queuing for high load
   - Add connection pooling if needed

## Security Considerations

### Input Validation

- Validate date ranges to prevent invalid queries
- Sanitize user inputs to prevent injection attacks
- Implement request size limits

### API Security

- Add CORS configuration for production
- Implement rate limiting to prevent abuse
- Add request logging for monitoring

### Data Security

- Ensure CSV files are read-only
- Implement proper error handling to avoid data leakage
- Add input sanitization for file paths

## Deployment Architecture

### Development Environment

- Local development with hot reload
- SQLite for development data (if needed)
- Simple file-based configuration

### Production Environment

- Docker containerization
- Reverse proxy (nginx) for static files
- Process management (systemd or supervisor)
- Log aggregation and monitoring

### Configuration Management

- Environment-based configuration
- Separate configs for dev/staging/production
- Secure handling of sensitive data