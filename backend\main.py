"""
股票板块数据分析系统 - FastAPI主应用
"""
import os
import json
from pathlib import Path
from typing import Dict, List, Optional

# 尝试导入FastAPI，如果失败则提供错误信息
try:
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import HTMLResponse
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    print("FastAPI未安装，请运行: pip install fastapi uvicorn")

if FASTAPI_AVAILABLE:
    # 创建FastAPI应用实例
    app = FastAPI(
        title="股票板块数据分析系统",
        description="基于历史数据的板块涨幅排名和季节性规律分析",
        version="1.0.0"
    )

    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 挂载静态文件服务
    if os.path.exists("frontend/static"):
        app.mount("/static", StaticFiles(directory="frontend/static"), name="static")

    @app.get("/")
    async def root():
        """根路径，返回系统信息"""
        # 检查数据目录和文件
        data_dir = Path("data")
        sector_codes_file = Path("sector_codes.json")

        # 统计数据文件
        csv_count = 0
        if data_dir.exists():
            csv_count = len(list(data_dir.glob("*.csv")))

        # 读取板块信息
        sector_count = 0
        if sector_codes_file.exists():
            try:
                with open(sector_codes_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if "申万一级行业" in data:
                    sector_count = len(data["申万一级行业"])
            except:
                pass

        return {
            "message": "股票板块数据分析系统",
            "version": "1.0.0",
            "status": "running",
            "data_info": {
                "csv_files": csv_count,
                "sectors": sector_count,
                "data_directory": str(data_dir),
                "data_available": data_dir.exists()
            },
            "features": [
                "时间段板块涨幅排名分析",
                "季节性规律发现",
                "交互式Web界面",
                "数据可视化图表"
            ]
        }

    @app.get("/health")
    async def health_check():
        """健康检查接口"""
        return {
            "status": "healthy",
            "timestamp": "2025-07-22",
            "services": {
                "web_server": "running",
                "data_loader": "ready",
                "analysis_engine": "ready"
            }
        }

    @app.get("/api/system-info")
    async def get_system_info():
        """获取系统详细信息"""
        data_dir = Path("data")
        sector_codes_file = Path("sector_codes.json")

        # 获取数据文件列表
        csv_files = []
        if data_dir.exists():
            csv_files = [f.name for f in data_dir.glob("*.csv")]
            csv_files.sort()

        # 获取板块信息
        sectors = {}
        if sector_codes_file.exists():
            try:
                with open(sector_codes_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if "申万一级行业" in data:
                    sectors = data["申万一级行业"]
            except Exception as e:
                pass

        return {
            "system": {
                "name": "股票板块数据分析系统",
                "version": "1.0.0",
                "description": "基于历史数据的板块涨幅排名和季节性规律分析"
            },
            "data": {
                "total_csv_files": len(csv_files),
                "date_range": {
                    "start": csv_files[0].replace('.csv', '') if csv_files else None,
                    "end": csv_files[-1].replace('.csv', '') if csv_files else None
                },
                "recent_files": csv_files[-5:] if len(csv_files) >= 5 else csv_files
            },
            "sectors": {
                "total_count": len(sectors),
                "sample_sectors": dict(list(sectors.items())[:5]) if sectors else {}
            }
        }

    @app.get("/", response_class=HTMLResponse)
    async def serve_frontend():
        """提供前端页面"""
        frontend_file = Path("frontend/index.html")
        if frontend_file.exists():
            with open(frontend_file, 'r', encoding='utf-8') as f:
                return HTMLResponse(content=f.read())
        else:
            return HTMLResponse(content="""
            <html>
                <head><title>股票板块数据分析系统</title></head>
                <body>
                    <h1>股票板块数据分析系统</h1>
                    <p>前端页面正在开发中...</p>
                    <p>请访问 <a href="/docs">/docs</a> 查看API文档</p>
                </body>
            </html>
            """)

if __name__ == "__main__":
    if FASTAPI_AVAILABLE:
        try:
            import uvicorn
            print("启动股票板块数据分析系统...")
            print("访问地址: http://localhost:8000")
            print("API文档: http://localhost:8000/docs")
            uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
        except ImportError:
            print("uvicorn未安装，请运行: pip install uvicorn")
    else:
        print("请先安装依赖: pip install fastapi uvicorn")
