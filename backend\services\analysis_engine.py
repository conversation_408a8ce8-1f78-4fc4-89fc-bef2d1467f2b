"""
股票板块涨幅排名分析引擎
提供期间涨幅计算、排名分析、板块筛选等核心功能
"""
import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple, Union
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from services.data_loader import SectorDataLoader

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AnalysisEngine:
    """
    股票板块涨幅排名分析引擎
    
    功能：
    - 期间涨幅计算
    - 板块排名分析
    - 数据筛选和过滤
    - 异常情况处理
    """
    
    def __init__(self, data_loader: Optional[SectorDataLoader] = None):
        """
        初始化分析引擎
        
        Args:
            data_loader: 数据加载器实例，如果为None则自动创建
        """
        self.data_loader = data_loader or SectorDataLoader()
        logger.info("涨幅排名分析引擎初始化完成")
    
    def calculate_period_returns(
        self, 
        start_date: str, 
        end_date: str, 
        sectors: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, Union[float, str]]]:
        """
        计算指定时间段内的板块涨幅
        
        Args:
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            sectors: 指定板块代码列表，如果为None则分析所有板块
            
        Returns:
            字典，键为板块代码，值为包含涨幅信息的字典
            {
                'sector_code': {
                    'sector_name': '板块名称',
                    'start_price': 起始价格,
                    'end_price': 结束价格,
                    'period_return': 期间涨幅(%),
                    'start_date': 实际开始日期,
                    'end_date': 实际结束日期,
                    'trading_days': 交易日数量,
                    'status': 'success' | 'error',
                    'error_message': 错误信息(如果有)
                }
            }
        """
        logger.info(f"开始计算期间涨幅: {start_date} 至 {end_date}")
        
        results = {}
        
        try:
            # 1. 数据完整性检查
            start_data = self._get_valid_date_data(start_date, 'start')
            end_data = self._get_valid_date_data(end_date, 'end')
            
            if start_data is None or end_data is None:
                logger.error("无法获取有效的起始或结束日期数据")
                return {}
            
            start_df, actual_start_date = start_data
            end_df, actual_end_date = end_df
            
            logger.info(f"实际分析日期: {actual_start_date} 至 {actual_end_date}")
            
            # 2. 获取板块代码映射
            sector_codes = self.data_loader.get_sector_info()
            
            # 3. 筛选指定板块
            if sectors:
                # 验证板块代码有效性
                valid_sectors = [s for s in sectors if s in sector_codes]
                if not valid_sectors:
                    logger.warning(f"指定的板块代码无效: {sectors}")
                    return {}
                target_sectors = valid_sectors
            else:
                target_sectors = list(sector_codes.keys())
            
            logger.info(f"分析板块数量: {len(target_sectors)}")
            
            # 4. 计算每个板块的涨幅
            for sector_code in target_sectors:
                try:
                    result = self._calculate_single_sector_return(
                        sector_code, start_df, end_df, 
                        actual_start_date, actual_end_date, sector_codes
                    )
                    results[sector_code] = result
                    
                except Exception as e:
                    logger.error(f"计算板块 {sector_code} 涨幅失败: {e}")
                    results[sector_code] = {
                        'sector_name': sector_codes.get(sector_code, sector_code),
                        'start_price': None,
                        'end_price': None,
                        'period_return': None,
                        'start_date': actual_start_date,
                        'end_date': actual_end_date,
                        'trading_days': None,
                        'status': 'error',
                        'error_message': str(e)
                    }
            
            # 5. 统计成功计算的板块数量
            success_count = sum(1 for r in results.values() if r['status'] == 'success')
            logger.info(f"成功计算 {success_count}/{len(target_sectors)} 个板块的涨幅")
            
            return results
            
        except Exception as e:
            logger.error(f"计算期间涨幅失败: {e}")
            return {}
    
    def _get_valid_date_data(self, target_date: str, date_type: str) -> Optional[Tuple[pd.DataFrame, str]]:
        """
        获取有效日期的数据，如果指定日期无数据则寻找最近的交易日
        
        Args:
            target_date: 目标日期
            date_type: 日期类型 ('start' 或 'end')
            
        Returns:
            (数据框, 实际日期) 或 None
        """
        # 首先尝试目标日期
        df = self.data_loader.load_date_data(target_date)
        if df is not None and not df.empty:
            return df, target_date
        
        # 如果目标日期无数据，寻找最近的交易日
        available_dates = self.data_loader.get_available_dates()
        target_date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()
        
        # 转换可用日期为date对象
        available_date_objs = []
        for date_str in available_dates:
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d').date()
                available_date_objs.append((date_obj, date_str))
            except ValueError:
                continue
        
        available_date_objs.sort()
        
        if date_type == 'start':
            # 对于开始日期，寻找目标日期之后的最近交易日
            for date_obj, date_str in available_date_objs:
                if date_obj >= target_date_obj:
                    df = self.data_loader.load_date_data(date_str)
                    if df is not None and not df.empty:
                        actual_date = date_obj.strftime('%Y-%m-%d')
                        logger.info(f"开始日期调整: {target_date} -> {actual_date}")
                        return df, actual_date
        else:
            # 对于结束日期，寻找目标日期之前的最近交易日
            for date_obj, date_str in reversed(available_date_objs):
                if date_obj <= target_date_obj:
                    df = self.data_loader.load_date_data(date_str)
                    if df is not None and not df.empty:
                        actual_date = date_obj.strftime('%Y-%m-%d')
                        logger.info(f"结束日期调整: {target_date} -> {actual_date}")
                        return df, actual_date
        
        logger.error(f"无法找到 {target_date} 附近的有效交易日数据")
        return None

    def _calculate_single_sector_return(
        self,
        sector_code: str,
        start_df: pd.DataFrame,
        end_df: pd.DataFrame,
        start_date: str,
        end_date: str,
        sector_codes: Dict[str, str]
    ) -> Dict[str, Union[float, str]]:
        """
        计算单个板块的期间涨幅

        Args:
            sector_code: 板块代码
            start_df: 开始日期数据
            end_df: 结束日期数据
            start_date: 开始日期
            end_date: 结束日期
            sector_codes: 板块代码映射

        Returns:
            包含涨幅信息的字典
        """
        # 查找板块数据
        start_sector_data = start_df[start_df['sector_code'] == sector_code]
        end_sector_data = end_df[end_df['sector_code'] == sector_code]

        if start_sector_data.empty:
            raise ValueError(f"开始日期 {start_date} 无板块 {sector_code} 数据")

        if end_sector_data.empty:
            raise ValueError(f"结束日期 {end_date} 无板块 {sector_code} 数据")

        # 获取价格数据
        start_price = float(start_sector_data.iloc[0]['close'])
        end_price = float(end_sector_data.iloc[0]['close'])

        # 计算涨幅
        if start_price == 0:
            raise ValueError(f"板块 {sector_code} 开始价格为0，无法计算涨幅")

        period_return = ((end_price - start_price) / start_price) * 100

        # 计算交易日数量
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        trading_days = (end_date_obj - start_date_obj).days + 1

        return {
            'sector_name': sector_codes.get(sector_code, sector_code),
            'start_price': round(start_price, 2),
            'end_price': round(end_price, 2),
            'period_return': round(period_return, 2),
            'start_date': start_date,
            'end_date': end_date,
            'trading_days': trading_days,
            'status': 'success',
            'error_message': None
        }

    def get_top_sectors(
        self,
        period_returns: Dict[str, Dict[str, Union[float, str]]],
        top_n: int = 5,
        sort_by: str = 'period_return',
        ascending: bool = False
    ) -> List[Dict[str, Union[str, float, int]]]:
        """
        获取涨幅排名前N的板块

        Args:
            period_returns: calculate_period_returns的返回结果
            top_n: 返回前N名，默认5
            sort_by: 排序字段，默认'period_return'
            ascending: 是否升序排列，默认False（降序）

        Returns:
            排序后的板块列表，每个元素包含排名信息
        """
        logger.info(f"获取涨幅排名前 {top_n} 名板块")

        # 过滤成功计算的板块
        valid_sectors = []
        for sector_code, data in period_returns.items():
            if data['status'] == 'success' and data[sort_by] is not None:
                sector_info = {
                    'sector_code': sector_code,
                    'sector_name': data['sector_name'],
                    'start_price': data['start_price'],
                    'end_price': data['end_price'],
                    'period_return': data['period_return'],
                    'start_date': data['start_date'],
                    'end_date': data['end_date'],
                    'trading_days': data['trading_days']
                }
                valid_sectors.append(sector_info)

        if not valid_sectors:
            logger.warning("没有有效的板块数据用于排名")
            return []

        # 按指定字段排序
        sorted_sectors = sorted(
            valid_sectors,
            key=lambda x: x[sort_by],
            reverse=not ascending
        )

        # 添加排名信息
        top_sectors = []
        for i, sector in enumerate(sorted_sectors[:top_n]):
            sector['rank'] = i + 1
            top_sectors.append(sector)

        logger.info(f"成功获取前 {len(top_sectors)} 名板块排名")
        return top_sectors

    def sector_filtering(
        self,
        period_returns: Dict[str, Dict[str, Union[float, str]]],
        filter_criteria: Dict[str, Union[str, List[str], float]]
    ) -> Dict[str, Dict[str, Union[float, str]]]:
        """
        根据条件筛选板块

        Args:
            period_returns: calculate_period_returns的返回结果
            filter_criteria: 筛选条件字典
                - 'sector_codes': 指定板块代码列表
                - 'sector_names': 指定板块名称列表（支持模糊匹配）
                - 'min_return': 最小涨幅
                - 'max_return': 最大涨幅
                - 'status': 状态筛选 ('success', 'error')

        Returns:
            筛选后的板块数据
        """
        logger.info(f"开始板块筛选，原始数据: {len(period_returns)} 个板块")

        filtered_results = {}

        for sector_code, data in period_returns.items():
            # 检查是否满足所有筛选条件
            if self._meets_filter_criteria(sector_code, data, filter_criteria):
                filtered_results[sector_code] = data

        logger.info(f"筛选完成，结果: {len(filtered_results)} 个板块")
        return filtered_results

    def _meets_filter_criteria(
        self,
        sector_code: str,
        data: Dict[str, Union[float, str]],
        criteria: Dict[str, Union[str, List[str], float]]
    ) -> bool:
        """
        检查板块是否满足筛选条件

        Args:
            sector_code: 板块代码
            data: 板块数据
            criteria: 筛选条件

        Returns:
            是否满足条件
        """
        # 状态筛选
        if 'status' in criteria:
            if data['status'] != criteria['status']:
                return False

        # 板块代码筛选
        if 'sector_codes' in criteria:
            if sector_code not in criteria['sector_codes']:
                return False

        # 板块名称筛选（模糊匹配）
        if 'sector_names' in criteria:
            sector_name = data.get('sector_name', '')
            name_match = False
            for target_name in criteria['sector_names']:
                if target_name in sector_name or sector_name in target_name:
                    name_match = True
                    break
            if not name_match:
                return False

        # 涨幅范围筛选（仅对成功计算的数据）
        if data['status'] == 'success' and data['period_return'] is not None:
            period_return = data['period_return']

            if 'min_return' in criteria:
                if period_return < criteria['min_return']:
                    return False

            if 'max_return' in criteria:
                if period_return > criteria['max_return']:
                    return False

        return True

    def get_analysis_summary(
        self,
        period_returns: Dict[str, Dict[str, Union[float, str]]]
    ) -> Dict[str, Union[int, float, str]]:
        """
        获取分析结果摘要统计

        Args:
            period_returns: calculate_period_returns的返回结果

        Returns:
            统计摘要信息
        """
        total_sectors = len(period_returns)
        success_sectors = sum(1 for data in period_returns.values() if data['status'] == 'success')
        error_sectors = total_sectors - success_sectors

        # 计算涨幅统计
        valid_returns = [
            data['period_return'] for data in period_returns.values()
            if data['status'] == 'success' and data['period_return'] is not None
        ]

        if valid_returns:
            avg_return = sum(valid_returns) / len(valid_returns)
            max_return = max(valid_returns)
            min_return = min(valid_returns)
            positive_count = sum(1 for r in valid_returns if r > 0)
            negative_count = sum(1 for r in valid_returns if r < 0)
            zero_count = sum(1 for r in valid_returns if r == 0)
        else:
            avg_return = max_return = min_return = 0
            positive_count = negative_count = zero_count = 0

        return {
            'total_sectors': total_sectors,
            'success_sectors': success_sectors,
            'error_sectors': error_sectors,
            'success_rate': round((success_sectors / total_sectors) * 100, 2) if total_sectors > 0 else 0,
            'average_return': round(avg_return, 2),
            'max_return': round(max_return, 2),
            'min_return': round(min_return, 2),
            'positive_sectors': positive_count,
            'negative_sectors': negative_count,
            'neutral_sectors': zero_count,
            'win_rate': round((positive_count / len(valid_returns)) * 100, 2) if valid_returns else 0
        }

    def validate_date_range(self, start_date: str, end_date: str) -> Dict[str, Union[bool, str]]:
        """
        验证日期范围的有效性

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            验证结果
        """
        try:
            # 日期格式验证
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

            # 日期逻辑验证
            if start_date_obj >= end_date_obj:
                return {
                    'valid': False,
                    'error': '开始日期必须早于结束日期'
                }

            # 检查日期范围是否过大
            days_diff = (end_date_obj - start_date_obj).days
            if days_diff > 365 * 2:  # 超过2年
                return {
                    'valid': False,
                    'error': '日期范围不能超过2年'
                }

            # 检查是否有可用数据
            available_dates = self.data_loader.get_available_dates()
            if not available_dates:
                return {
                    'valid': False,
                    'error': '系统中没有可用的历史数据'
                }

            # 检查日期范围是否在数据范围内
            earliest_date = datetime.strptime(available_dates[0], '%Y%m%d').date()
            latest_date = datetime.strptime(available_dates[-1], '%Y%m%d').date()

            if end_date_obj < earliest_date or start_date_obj > latest_date:
                return {
                    'valid': False,
                    'error': f'日期范围超出数据范围 ({earliest_date} 至 {latest_date})'
                }

            return {
                'valid': True,
                'message': '日期范围验证通过',
                'days_diff': days_diff,
                'data_range': f'{earliest_date} 至 {latest_date}'
            }

        except ValueError as e:
            return {
                'valid': False,
                'error': f'日期格式错误: {e}'
            }

    def get_available_sectors(self) -> Dict[str, str]:
        """
        获取所有可用的板块信息

        Returns:
            板块代码到名称的映射
        """
        return self.data_loader.get_sector_info()

    def batch_analysis(
        self,
        analysis_configs: List[Dict[str, Union[str, List[str], int]]]
    ) -> Dict[str, Dict]:
        """
        批量分析多个时间段或条件

        Args:
            analysis_configs: 分析配置列表，每个配置包含：
                - 'name': 分析名称
                - 'start_date': 开始日期
                - 'end_date': 结束日期
                - 'sectors': 板块列表（可选）
                - 'top_n': 排名数量（可选）

        Returns:
            批量分析结果
        """
        logger.info(f"开始批量分析，配置数量: {len(analysis_configs)}")

        batch_results = {}

        for i, config in enumerate(analysis_configs):
            config_name = config.get('name', f'分析_{i+1}')

            try:
                # 执行单次分析
                period_returns = self.calculate_period_returns(
                    config['start_date'],
                    config['end_date'],
                    config.get('sectors')
                )

                # 获取排名
                top_n = config.get('top_n', 5)
                top_sectors = self.get_top_sectors(period_returns, top_n)

                # 获取摘要
                summary = self.get_analysis_summary(period_returns)

                batch_results[config_name] = {
                    'config': config,
                    'period_returns': period_returns,
                    'top_sectors': top_sectors,
                    'summary': summary,
                    'status': 'success'
                }

                logger.info(f"完成分析: {config_name}")

            except Exception as e:
                logger.error(f"分析失败: {config_name}, 错误: {e}")
                batch_results[config_name] = {
                    'config': config,
                    'status': 'error',
                    'error_message': str(e)
                }

        logger.info(f"批量分析完成，成功: {sum(1 for r in batch_results.values() if r['status'] == 'success')}/{len(analysis_configs)}")
        return batch_results
