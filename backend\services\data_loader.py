"""
数据加载服务模块
提供高效的CSV数据读取、缓存和查询功能
"""
import os
import json
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from functools import lru_cache
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SectorDataLoader:
    """
    股票板块数据加载器
    
    功能：
    - CSV文件读取和缓存
    - 日期范围数据查询
    - 数据格式验证
    - 内存优化管理
    """
    
    def __init__(self, data_dir: str = "data", sector_codes_file: str = "sector_codes.json"):
        """
        初始化数据加载器
        
        Args:
            data_dir: 数据文件目录
            sector_codes_file: 板块代码映射文件
        """
        self.data_dir = Path(data_dir)
        self.sector_codes_file = Path(sector_codes_file)
        
        # 验证目录和文件存在
        if not self.data_dir.exists():
            raise FileNotFoundError(f"数据目录不存在: {self.data_dir}")
        
        if not self.sector_codes_file.exists():
            raise FileNotFoundError(f"板块代码文件不存在: {self.sector_codes_file}")
        
        # 加载板块代码映射
        self.sector_codes = self._load_sector_codes()
        
        # 内存缓存
        self._cache = {}
        self._cache_size_limit = 50  # 最大缓存文件数
        
        # 可用日期列表缓存
        self._available_dates = None
        
        logger.info(f"数据加载器初始化完成，数据目录: {self.data_dir}")
        logger.info(f"板块数量: {len(self.sector_codes)}")
    
    def _load_sector_codes(self) -> Dict[str, str]:
        """加载板块代码映射"""
        try:
            with open(self.sector_codes_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取申万一级行业数据
            if "申万一级行业" in data:
                return data["申万一级行业"]
            else:
                logger.warning("板块代码文件中未找到'申万一级行业'分类")
                return {}
                
        except Exception as e:
            logger.error(f"加载板块代码失败: {e}")
            return {}
    
    def _get_csv_filename(self, date_str: str) -> str:
        """
        根据日期字符串生成CSV文件名
        
        Args:
            date_str: 日期字符串，格式：YYYY-MM-DD 或 YYYYMMDD
            
        Returns:
            CSV文件名
        """
        # 标准化日期格式
        if '-' in date_str:
            date_str = date_str.replace('-', '')
        
        return f"{date_str}.csv"
    
    def _validate_csv_data(self, df: pd.DataFrame) -> bool:
        """
        验证CSV数据格式
        
        Args:
            df: 数据框
            
        Returns:
            是否格式正确
        """
        required_columns = [
            'sector_code', 'sector_name', 'date', 'open', 'close', 
            'high', 'low', 'volume', 'amount', 'amplitude', 
            'change_pct', 'change_amount', 'turnover_rate'
        ]
        
        # 检查列名
        if not all(col in df.columns for col in required_columns):
            missing_cols = [col for col in required_columns if col not in df.columns]
            logger.error(f"CSV数据缺少必要列: {missing_cols}")
            return False
        
        # 检查数据不为空
        if df.empty:
            logger.error("CSV数据为空")
            return False
        
        return True
    
    def _manage_cache(self):
        """管理缓存大小，使用LRU策略"""
        if len(self._cache) > self._cache_size_limit:
            # 移除最旧的缓存项
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
            logger.debug(f"缓存已满，移除最旧项: {oldest_key}")
    
    def load_date_data(self, date_str: str) -> Optional[pd.DataFrame]:
        """
        加载指定日期的数据
        
        Args:
            date_str: 日期字符串，格式：YYYY-MM-DD 或 YYYYMMDD
            
        Returns:
            数据框，如果文件不存在或读取失败则返回None
        """
        # 检查缓存
        cache_key = date_str.replace('-', '')
        if cache_key in self._cache:
            logger.debug(f"从缓存加载数据: {date_str}")
            return self._cache[cache_key].copy()
        
        # 构建文件路径
        csv_filename = self._get_csv_filename(date_str)
        csv_path = self.data_dir / csv_filename
        
        if not csv_path.exists():
            logger.warning(f"数据文件不存在: {csv_path}")
            return None
        
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_path, encoding='utf-8')
            
            # 验证数据格式
            if not self._validate_csv_data(df):
                logger.error(f"数据格式验证失败: {csv_path}")
                return None
            
            # 数据类型转换
            df['date'] = pd.to_datetime(df['date'])
            numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount', 
                             'amplitude', 'change_pct', 'change_amount', 'turnover_rate']
            
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 添加到缓存
            self._manage_cache()
            self._cache[cache_key] = df.copy()
            
            logger.debug(f"成功加载数据: {csv_path}, 记录数: {len(df)}")
            return df
            
        except Exception as e:
            logger.error(f"读取CSV文件失败: {csv_path}, 错误: {e}")
            return None

    def load_date_range(self, start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """
        批量加载日期范围内的数据

        Args:
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD

        Returns:
            字典，键为日期字符串，值为数据框
        """
        result = {}
        available_dates = self.get_available_dates()

        # 转换日期格式
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

        # 筛选日期范围内的文件
        target_dates = []
        for date_str in available_dates:
            try:
                file_date = datetime.strptime(date_str, '%Y%m%d').date()
                if start_date_obj <= file_date <= end_date_obj:
                    target_dates.append(date_str)
            except ValueError:
                continue

        logger.info(f"加载日期范围 {start_date} 至 {end_date}，找到 {len(target_dates)} 个交易日")

        # 批量加载数据
        for date_str in target_dates:
            df = self.load_date_data(date_str)
            if df is not None:
                result[date_str] = df

        logger.info(f"成功加载 {len(result)} 个交易日的数据")
        return result

    def get_available_dates(self) -> List[str]:
        """
        获取所有可用的交易日期

        Returns:
            日期字符串列表，格式：YYYYMMDD
        """
        if self._available_dates is not None:
            return self._available_dates

        # 扫描数据目录
        csv_files = list(self.data_dir.glob("*.csv"))
        dates = []

        for csv_file in csv_files:
            filename = csv_file.stem  # 不包含扩展名的文件名

            # 验证文件名格式（YYYYMMDD）
            if len(filename) == 8 and filename.isdigit():
                try:
                    # 验证是否为有效日期
                    datetime.strptime(filename, '%Y%m%d')
                    dates.append(filename)
                except ValueError:
                    continue

        # 排序
        dates.sort()
        self._available_dates = dates

        logger.info(f"发现 {len(dates)} 个可用交易日")
        if dates:
            logger.info(f"日期范围: {dates[0]} 至 {dates[-1]}")

        return dates

    def get_date_range_info(self) -> Dict[str, str]:
        """
        获取数据日期范围信息

        Returns:
            包含开始日期和结束日期的字典
        """
        dates = self.get_available_dates()
        if not dates:
            return {"start_date": "", "end_date": "", "total_days": 0}

        start_date = datetime.strptime(dates[0], '%Y%m%d').strftime('%Y-%m-%d')
        end_date = datetime.strptime(dates[-1], '%Y%m%d').strftime('%Y-%m-%d')

        return {
            "start_date": start_date,
            "end_date": end_date,
            "total_days": len(dates)
        }

    def get_sector_info(self) -> Dict[str, str]:
        """
        获取板块信息

        Returns:
            板块代码到名称的映射
        """
        return self.sector_codes.copy()

    def validate_date_exists(self, date_str: str) -> bool:
        """
        验证指定日期的数据是否存在

        Args:
            date_str: 日期字符串，格式：YYYY-MM-DD 或 YYYYMMDD

        Returns:
            数据是否存在
        """
        csv_filename = self._get_csv_filename(date_str)
        csv_path = self.data_dir / csv_filename
        return csv_path.exists()

    def get_cache_info(self) -> Dict[str, Union[int, List[str]]]:
        """
        获取缓存信息

        Returns:
            缓存统计信息
        """
        return {
            "cached_dates": list(self._cache.keys()),
            "cache_size": len(self._cache),
            "cache_limit": self._cache_size_limit
        }

    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self._available_dates = None
        logger.info("缓存已清空")
