"""
计算工具函数模块
提供各种数学计算和统计分析功能
"""
import math
from typing import List, Dict, Union, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


def calculate_return_rate(start_price: float, end_price: float) -> float:
    """
    计算收益率
    
    Args:
        start_price: 起始价格
        end_price: 结束价格
        
    Returns:
        收益率（百分比）
        
    Raises:
        ValueError: 起始价格为0或负数
    """
    if start_price <= 0:
        raise ValueError(f"起始价格必须大于0，当前值: {start_price}")
    
    return ((end_price - start_price) / start_price) * 100


def calculate_annualized_return(total_return: float, days: int) -> float:
    """
    计算年化收益率
    
    Args:
        total_return: 总收益率（百分比）
        days: 投资天数
        
    Returns:
        年化收益率（百分比）
    """
    if days <= 0:
        raise ValueError(f"投资天数必须大于0，当前值: {days}")
    
    # 假设一年有252个交易日
    trading_days_per_year = 252
    return ((1 + total_return / 100) ** (trading_days_per_year / days) - 1) * 100


def calculate_volatility(returns: List[float]) -> float:
    """
    计算收益率波动率（标准差）
    
    Args:
        returns: 收益率列表
        
    Returns:
        波动率（百分比）
    """
    if len(returns) < 2:
        return 0.0
    
    mean_return = sum(returns) / len(returns)
    variance = sum((r - mean_return) ** 2 for r in returns) / (len(returns) - 1)
    return math.sqrt(variance)


def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.03) -> float:
    """
    计算夏普比率
    
    Args:
        returns: 收益率列表（小数形式，如0.05表示5%）
        risk_free_rate: 无风险利率，默认3%
        
    Returns:
        夏普比率
    """
    if len(returns) < 2:
        return 0.0
    
    mean_return = sum(returns) / len(returns)
    volatility = calculate_volatility([r * 100 for r in returns]) / 100  # 转换为小数形式
    
    if volatility == 0:
        return 0.0
    
    return (mean_return - risk_free_rate) / volatility


def calculate_max_drawdown(prices: List[float]) -> Dict[str, float]:
    """
    计算最大回撤
    
    Args:
        prices: 价格序列
        
    Returns:
        包含最大回撤信息的字典
    """
    if len(prices) < 2:
        return {'max_drawdown': 0.0, 'peak_price': 0.0, 'trough_price': 0.0}
    
    peak_price = prices[0]
    max_drawdown = 0.0
    peak_idx = 0
    trough_idx = 0
    
    for i, price in enumerate(prices):
        if price > peak_price:
            peak_price = price
            peak_idx = i
        
        drawdown = (peak_price - price) / peak_price
        if drawdown > max_drawdown:
            max_drawdown = drawdown
            trough_idx = i
    
    return {
        'max_drawdown': max_drawdown * 100,  # 转换为百分比
        'peak_price': peak_price,
        'trough_price': prices[trough_idx] if trough_idx < len(prices) else 0.0,
        'peak_index': peak_idx,
        'trough_index': trough_idx
    }


def calculate_win_rate(returns: List[float]) -> Dict[str, Union[float, int]]:
    """
    计算胜率统计
    
    Args:
        returns: 收益率列表
        
    Returns:
        胜率统计信息
    """
    if not returns:
        return {
            'win_rate': 0.0,
            'positive_count': 0,
            'negative_count': 0,
            'neutral_count': 0,
            'total_count': 0
        }
    
    positive_count = sum(1 for r in returns if r > 0)
    negative_count = sum(1 for r in returns if r < 0)
    neutral_count = sum(1 for r in returns if r == 0)
    total_count = len(returns)
    
    win_rate = (positive_count / total_count) * 100 if total_count > 0 else 0.0
    
    return {
        'win_rate': win_rate,
        'positive_count': positive_count,
        'negative_count': negative_count,
        'neutral_count': neutral_count,
        'total_count': total_count
    }


def calculate_percentiles(values: List[float], percentiles: List[float] = [25, 50, 75]) -> Dict[str, float]:
    """
    计算百分位数
    
    Args:
        values: 数值列表
        percentiles: 要计算的百分位数列表
        
    Returns:
        百分位数字典
    """
    if not values:
        return {f'p{p}': 0.0 for p in percentiles}
    
    sorted_values = sorted(values)
    n = len(sorted_values)
    
    result = {}
    for p in percentiles:
        if p < 0 or p > 100:
            continue
        
        if p == 0:
            result[f'p{p}'] = sorted_values[0]
        elif p == 100:
            result[f'p{p}'] = sorted_values[-1]
        else:
            index = (p / 100) * (n - 1)
            lower_index = int(index)
            upper_index = min(lower_index + 1, n - 1)
            
            if lower_index == upper_index:
                result[f'p{p}'] = sorted_values[lower_index]
            else:
                # 线性插值
                weight = index - lower_index
                result[f'p{p}'] = (
                    sorted_values[lower_index] * (1 - weight) + 
                    sorted_values[upper_index] * weight
                )
    
    return result


def calculate_correlation(x: List[float], y: List[float]) -> float:
    """
    计算皮尔逊相关系数
    
    Args:
        x: 第一个数据序列
        y: 第二个数据序列
        
    Returns:
        相关系数 (-1 到 1)
    """
    if len(x) != len(y) or len(x) < 2:
        return 0.0
    
    n = len(x)
    mean_x = sum(x) / n
    mean_y = sum(y) / n
    
    numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
    
    sum_sq_x = sum((x[i] - mean_x) ** 2 for i in range(n))
    sum_sq_y = sum((y[i] - mean_y) ** 2 for i in range(n))
    
    denominator = math.sqrt(sum_sq_x * sum_sq_y)
    
    if denominator == 0:
        return 0.0
    
    return numerator / denominator


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 分母为0时的默认值
        
    Returns:
        除法结果或默认值
    """
    if denominator == 0:
        return default
    return numerator / denominator


def round_to_precision(value: float, precision: int = 2) -> float:
    """
    四舍五入到指定精度
    
    Args:
        value: 要四舍五入的值
        precision: 小数位数
        
    Returns:
        四舍五入后的值
    """
    if math.isnan(value) or math.isinf(value):
        return 0.0
    
    return round(value, precision)


def validate_numeric_list(values: List[Union[int, float]], allow_empty: bool = False) -> bool:
    """
    验证数值列表的有效性
    
    Args:
        values: 数值列表
        allow_empty: 是否允许空列表
        
    Returns:
        是否有效
    """
    if not values and not allow_empty:
        return False
    
    for value in values:
        if not isinstance(value, (int, float)):
            return False
        if math.isnan(value) or math.isinf(value):
            return False
    
    return True
