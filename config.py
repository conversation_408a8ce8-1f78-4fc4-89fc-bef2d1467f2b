"""
系统配置文件
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据相关配置
DATA_DIR = PROJECT_ROOT / "data"
SECTOR_CODES_FILE = PROJECT_ROOT / "sector_codes.json"

# Web服务配置
HOST = "0.0.0.0"
PORT = 8000
DEBUG = True

# 缓存配置
CACHE_SIZE = 100  # 最大缓存文件数量
CACHE_TTL = 3600  # 缓存过期时间（秒）

# 分析配置
DEFAULT_TOP_N = 5  # 默认显示前N名
MAX_TOP_N = 20     # 最大显示数量

# 预设时间段配置
PRESET_PERIODS = {
    "july": {
        "name": "7月份",
        "description": "每年7月1日-7月31日",
        "months": [7]
    },
    "winter": {
        "name": "冬季",
        "description": "每年12月-次年2月",
        "months": [12, 1, 2]
    },
    "summer": {
        "name": "夏季",
        "description": "每年6月-8月",
        "months": [6, 7, 8]
    },
    "spring": {
        "name": "春季",
        "description": "每年3月-5月",
        "months": [3, 4, 5]
    },
    "autumn": {
        "name": "秋季",
        "description": "每年9月-11月",
        "months": [9, 10, 11]
    }
}

# 验证配置
def validate_config():
    """验证配置是否正确"""
    if not DATA_DIR.exists():
        raise FileNotFoundError(f"数据目录不存在: {DATA_DIR}")
    
    if not SECTOR_CODES_FILE.exists():
        raise FileNotFoundError(f"板块代码文件不存在: {SECTOR_CODES_FILE}")
    
    print("✓ 配置验证通过")
    return True

if __name__ == "__main__":
    validate_config()
