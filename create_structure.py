#!/usr/bin/env python3
"""
创建Web分析系统的项目结构
"""
import os

def create_directory_structure():
    """创建项目目录结构"""
    directories = [
        # 后端目录结构
        "backend",
        "backend/api",
        "backend/services", 
        "backend/models",
        "backend/utils",
        
        # 前端目录结构
        "frontend",
        "frontend/static",
        "frontend/static/css",
        "frontend/static/js",
        "frontend/static/lib"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✓ 创建目录: {directory}")
        except Exception as e:
            print(f"✗ 创建目录失败 {directory}: {e}")
    
    # 创建__init__.py文件
    init_files = [
        "backend/__init__.py",
        "backend/api/__init__.py", 
        "backend/services/__init__.py",
        "backend/models/__init__.py",
        "backend/utils/__init__.py"
    ]
    
    for init_file in init_files:
        try:
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('"""模块初始化文件"""\n')
            print(f"✓ 创建文件: {init_file}")
        except Exception as e:
            print(f"✗ 创建文件失败 {init_file}: {e}")

if __name__ == "__main__":
    print("开始创建项目结构...")
    create_directory_structure()
    print("项目结构创建完成！")
