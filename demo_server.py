#!/usr/bin/env python3
"""
股票板块数据分析系统 - 演示服务器
使用Python内置HTTP服务器提供演示
"""
import http.server
import socketserver
import webbrowser
import json
import os
from pathlib import Path
import threading
import time

class DemoHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            # 提供前端页面
            self.serve_frontend()
        elif self.path == '/api/system-info':
            # 提供系统信息API
            self.serve_system_info()
        elif self.path == '/health':
            # 健康检查
            self.serve_health_check()
        elif self.path == '/api/demo-data':
            # 演示数据
            self.serve_demo_data()
        else:
            # 默认文件服务
            super().do_GET()
    
    def serve_frontend(self):
        """提供前端页面"""
        frontend_file = Path("frontend/index.html")
        if frontend_file.exists():
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            with open(frontend_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.wfile.write(content.encode('utf-8'))
        else:
            self.send_error(404, "前端页面未找到")
    
    def serve_system_info(self):
        """提供系统信息API"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # 收集系统信息
        data_dir = Path("data")
        sector_codes_file = Path("sector_codes.json")
        
        # 统计CSV文件
        csv_files = []
        if data_dir.exists():
            csv_files = [f.name for f in data_dir.glob("*.csv")]
            csv_files.sort()
        
        # 读取板块信息
        sectors = {}
        if sector_codes_file.exists():
            try:
                with open(sector_codes_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if "申万一级行业" in data:
                    sectors = data["申万一级行业"]
            except:
                pass
        
        system_info = {
            "system": {
                "name": "股票板块数据分析系统",
                "version": "1.0.0",
                "mode": "演示模式",
                "description": "基于历史数据的板块涨幅排名和季节性规律分析"
            },
            "data": {
                "total_csv_files": len(csv_files),
                "date_range": {
                    "start": csv_files[0].replace('.csv', '') if csv_files else None,
                    "end": csv_files[-1].replace('.csv', '') if csv_files else None
                },
                "recent_files": csv_files[-5:] if len(csv_files) >= 5 else csv_files
            },
            "sectors": {
                "total_count": len(sectors),
                "sample_sectors": dict(list(sectors.items())[:5]) if sectors else {}
            },
            "features": {
                "ranking_analysis": "时间段板块涨幅排名分析",
                "seasonal_analysis": "季节性规律发现",
                "data_visualization": "交互式数据可视化",
                "export_function": "结果导出功能"
            }
        }
        
        response = json.dumps(system_info, ensure_ascii=False, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def serve_health_check(self):
        """健康检查API"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        health_data = {
            "status": "healthy",
            "mode": "demo_server",
            "message": "股票板块数据分析系统演示服务器运行正常",
            "timestamp": "2025-07-22",
            "services": {
                "web_server": "running",
                "data_loader": "ready",
                "analysis_engine": "demo_mode"
            }
        }
        
        response = json.dumps(health_data, ensure_ascii=False, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def serve_demo_data(self):
        """提供演示数据"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        demo_data = {
            "ranking_demo": {
                "period": "2023-07-01 至 2023-07-31",
                "results": [
                    {"rank": 1, "sector_name": "电力行业", "sector_code": "BK0427", "return": 15.2},
                    {"rank": 2, "sector_name": "煤炭开采", "sector_code": "BK0437", "return": 12.8},
                    {"rank": 3, "sector_name": "电网设备", "sector_code": "BK0428", "return": 11.5},
                    {"rank": 4, "sector_name": "新能源", "sector_code": "BK0451", "return": 9.3},
                    {"rank": 5, "sector_name": "公用事业", "sector_code": "BK0440", "return": 8.7}
                ]
            },
            "seasonal_demo": {
                "sector": "电力行业",
                "months": [6, 7, 8],
                "historical_performance": [
                    {"year": 2020, "return": 8.5},
                    {"year": 2021, "return": 12.3},
                    {"year": 2022, "return": 6.8},
                    {"year": 2023, "return": 14.2},
                    {"year": 2024, "return": 9.7}
                ],
                "statistics": {
                    "average_return": 10.3,
                    "win_rate": 100,
                    "significance": "p < 0.05"
                }
            }
        }
        
        response = json.dumps(demo_data, ensure_ascii=False, indent=2)
        self.wfile.write(response.encode('utf-8'))

def start_demo_server(port=8000):
    """启动演示服务器"""
    try:
        with socketserver.TCPServer(("", port), DemoHandler) as httpd:
            print("=" * 60)
            print("🚀 股票板块数据分析系统 - 演示服务器")
            print("=" * 60)
            print(f"✓ 服务器启动成功")
            print(f"✓ 访问地址: http://localhost:{port}")
            print(f"✓ 系统信息: http://localhost:{port}/api/system-info")
            print(f"✓ 健康检查: http://localhost:{port}/health")
            print(f"✓ 演示数据: http://localhost:{port}/api/demo-data")
            print("✓ 按 Ctrl+C 停止服务器")
            print("=" * 60)
            
            # 延迟打开浏览器
            def open_browser():
                time.sleep(2)
                try:
                    webbrowser.open(f'http://localhost:{port}')
                    print("✓ 已尝试打开浏览器")
                except:
                    print("⚠ 无法自动打开浏览器，请手动访问上述地址")
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n✓ 服务器已停止")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"✗ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_demo_server(port + 1)
        else:
            print(f"✗ 启动服务器失败: {e}")
    except Exception as e:
        print(f"✗ 启动服务器失败: {e}")

def check_project_files():
    """检查项目文件"""
    print("检查项目文件...")
    
    required_files = [
        "frontend/index.html",
        "sector_codes.json",
        "backend/main.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print(f"✗ 缺少文件: {', '.join(missing_files)}")
        return False
    
    # 检查数据目录
    data_dir = Path("data")
    if data_dir.exists():
        csv_count = len(list(data_dir.glob("*.csv")))
        print(f"✓ data/ 目录存在，包含 {csv_count} 个CSV文件")
    else:
        print("⚠ data/ 目录不存在，部分功能可能受限")
    
    return True

if __name__ == "__main__":
    print("股票板块数据分析系统 - 演示启动器")
    print("=" * 60)
    
    if check_project_files():
        print("\n项目文件检查通过，启动演示服务器...")
        start_demo_server()
    else:
        print("\n项目文件不完整，请检查文件结构")
        input("按回车键退出...")
