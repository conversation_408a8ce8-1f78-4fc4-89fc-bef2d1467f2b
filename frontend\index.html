<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票板块数据分析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
        }

        header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin-bottom: 25px;
        }

        .status-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .feature-card ul {
            list-style: none;
        }

        .feature-card li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .feature-card li:last-child {
            border-bottom: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-completed { background: #27ae60; }
        .status-progress { background: #f39c12; }
        .status-pending { background: #95a5a6; }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin-top: 25px;
        }

        .demo-button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .data-preview {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }

        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 股票板块数据分析系统</h1>
            <p>基于历史数据的板块涨幅排名和季节性规律分析</p>
            <div style="margin-top: 15px;">
                <span class="status-indicator status-completed"></span>
                <strong>系统状态：运行中</strong>
            </div>
        </header>

        <!-- 系统状态概览 -->
        <div class="status-card">
            <h3>📊 系统状态概览</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🏗️ 项目架构</h4>
                    <ul>
                        <li><span class="status-indicator status-completed"></span>后端架构 (FastAPI)</li>
                        <li><span class="status-indicator status-completed"></span>前端界面 (HTML/CSS/JS)</li>
                        <li><span class="status-indicator status-completed"></span>数据加载服务</li>
                        <li><span class="status-indicator status-progress"></span>分析引擎 (开发中)</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📈 数据资源</h4>
                    <ul>
                        <li><span class="status-indicator status-completed"></span>历史数据 (2020-2025)</li>
                        <li><span class="status-indicator status-completed"></span>板块分类 (申万一级)</li>
                        <li><span class="status-indicator status-completed"></span>CSV格式数据</li>
                        <li><span class="status-indicator status-completed"></span>1000+ 交易日数据</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔧 核心功能</h4>
                    <ul>
                        <li><span class="status-indicator status-progress"></span>涨幅排名分析</li>
                        <li><span class="status-indicator status-progress"></span>季节性规律发现</li>
                        <li><span class="status-indicator status-progress"></span>数据可视化</li>
                        <li><span class="status-indicator status-pending"></span>结果导出</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 数据预览 -->
        <div class="demo-section">
            <h3>📋 数据样本预览</h3>
            <p>系统基于以下格式的历史数据进行分析：</p>

            <div class="data-preview">
sector_code,sector_name,date,open,close,high,low,volume,amount,amplitude,change_pct,change_amount,turnover_rate
BK0420,航空机场,2023-01-05,5237.79,5293.22,5304.94,5215.8,4888713,4724260759.17,1.7,1.21,63.37,0.51
BK0421,铁路公路,2023-01-05,6218.57,6226.76,6254.85,6171.07,3771763,2577420673.05,1.35,0.22,13.38,0.37
BK0422,物流行业,2023-01-05,6194.17,6235.03,6236.63,6168.66,8388146,8065121452.73,1.1,0.74,45.58,0.95
            </div>

            <div class="highlight">
                <strong>数据说明：</strong>包含板块代码、名称、日期、开盘价、收盘价、最高价、最低价、成交量、成交额、振幅、涨跌幅、涨跌额、换手率等13个关键字段。
            </div>
        </div>

        <!-- 功能演示 -->
        <div class="demo-section">
            <h3>🎯 核心功能演示</h3>
            <p>以下是系统的主要分析功能：</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📊 时间段涨幅排名</h4>
                    <p>分析指定时间段内板块涨幅表现，支持：</p>
                    <ul>
                        <li>自定义时间范围查询</li>
                        <li>预设时间段（7月、冬季等）</li>
                        <li>排名数量可调整（默认前5名）</li>
                        <li>交互式图表展示</li>
                    </ul>
                    <button class="demo-button" onclick="showRankingDemo()">查看演示</button>
                </div>

                <div class="feature-card">
                    <h4>🔍 季节性规律发现</h4>
                    <p>挖掘板块在特定月份的历史表现规律：</p>
                    <ul>
                        <li>特定板块月度表现分析</li>
                        <li>多年历史数据对比</li>
                        <li>统计显著性验证</li>
                        <li>季节性模式识别</li>
                    </ul>
                    <button class="demo-button" onclick="showSeasonalDemo()">查看演示</button>
                </div>
            </div>
        </div>

        <!-- 技术架构 -->
        <div class="demo-section">
            <h3>⚙️ 技术架构</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>后端技术栈</h4>
                    <ul>
                        <li><strong>FastAPI</strong> - 高性能Web框架</li>
                        <li><strong>pandas</strong> - 数据处理和分析</li>
                        <li><strong>uvicorn</strong> - ASGI服务器</li>
                        <li><strong>Python</strong> - 核心开发语言</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>前端技术栈</h4>
                    <ul>
                        <li><strong>HTML5/CSS3</strong> - 响应式界面</li>
                        <li><strong>JavaScript</strong> - 交互逻辑</li>
                        <li><strong>Chart.js</strong> - 数据可视化</li>
                        <li><strong>RESTful API</strong> - 前后端通信</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="demo-section">
            <h3>ℹ️ 系统信息</h3>
            <div class="data-preview">
版本: 1.0.0
开发状态: 演示版本
数据范围: 2020年5月 - 2025年7月
板块数量: 40+ 申万一级行业分类
数据文件: 1000+ CSV文件
更新频率: 每日更新（生产环境）
            </div>

            <div class="highlight">
                <strong>注意：</strong>当前为演示版本，完整功能需要安装Python依赖包。请运行 <code>pip install -r requirements.txt</code> 安装依赖后启动FastAPI服务器。
            </div>
        </div>
    </div>

    <script>
        function showRankingDemo() {
            alert('涨幅排名分析功能演示：\n\n1. 选择时间范围：2023-07-01 至 2023-07-31\n2. 分析结果：\n   - 第1名：电力行业 (+15.2%)\n   - 第2名：煤炭开采 (+12.8%)\n   - 第3名：电网设备 (+11.5%)\n   - 第4名：新能源 (+9.3%)\n   - 第5名：公用事业 (+8.7%)\n\n注：以上为模拟数据，实际结果基于真实历史数据计算。');
        }

        function showSeasonalDemo() {
            alert('季节性规律分析演示：\n\n分析板块：电力行业\n分析月份：6-8月（夏季）\n\n历史表现：\n- 2020年夏季：+8.5%\n- 2021年夏季：+12.3%\n- 2022年夏季：+6.8%\n- 2023年夏季：+14.2%\n- 2024年夏季：+9.7%\n\n统计结果：\n- 平均涨幅：+10.3%\n- 胜率：100% (5/5)\n- 显著性：p < 0.05\n\n结论：电力行业在夏季具有显著的正向表现规律。');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('股票板块数据分析系统已加载');
            console.log('系统状态：演示模式');
            console.log('如需完整功能，请安装依赖并启动FastAPI服务器');
        });
    </script>
</body>
</html>
