<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票板块数据分析系统</title>
    <link rel="stylesheet" href="static/css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>股票板块数据分析系统</h1>
            <p>基于历史数据的板块涨幅排名和季节性规律分析</p>
        </header>
        
        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="ranking">涨幅排名分析</button>
            <button class="tab-btn" data-tab="seasonal">季节性规律分析</button>
        </nav>
        
        <main>
            <!-- 涨幅排名分析面板 -->
            <div id="ranking-panel" class="tab-panel active">
                <div class="controls">
                    <h3>查询参数</h3>
                    <div class="control-group">
                        <label>时间范围:</label>
                        <input type="date" id="start-date" />
                        <span>至</span>
                        <input type="date" id="end-date" />
                    </div>
                    <div class="control-group">
                        <label>预设时间段:</label>
                        <select id="preset-period">
                            <option value="">自定义</option>
                            <option value="july">7月份</option>
                            <option value="winter">冬季</option>
                            <option value="summer">夏季</option>
                            <option value="spring">春季</option>
                            <option value="autumn">秋季</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>显示数量:</label>
                        <input type="number" id="top-n" value="5" min="1" max="20" />
                    </div>
                    <button id="query-ranking" class="btn-primary">查询排名</button>
                </div>
                
                <div class="results">
                    <div id="ranking-loading" class="loading hidden">查询中...</div>
                    <div id="ranking-error" class="error hidden"></div>
                    <div id="ranking-results" class="hidden">
                        <h3>涨幅排名结果</h3>
                        <div class="chart-container">
                            <canvas id="ranking-chart"></canvas>
                        </div>
                        <div class="table-container">
                            <table id="ranking-table">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>板块名称</th>
                                        <th>板块代码</th>
                                        <th>期间涨幅(%)</th>
                                        <th>起始价格</th>
                                        <th>结束价格</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <button id="export-ranking" class="btn-secondary">导出结果</button>
                    </div>
                </div>
            </div>
            
            <!-- 季节性分析面板 -->
            <div id="seasonal-panel" class="tab-panel">
                <div class="controls">
                    <h3>分析参数</h3>
                    <div class="control-group">
                        <label>选择板块:</label>
                        <select id="sector-select">
                            <option value="">请选择板块</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>分析月份:</label>
                        <div class="month-checkboxes">
                            <label><input type="checkbox" value="1"> 1月</label>
                            <label><input type="checkbox" value="2"> 2月</label>
                            <label><input type="checkbox" value="3"> 3月</label>
                            <label><input type="checkbox" value="4"> 4月</label>
                            <label><input type="checkbox" value="5"> 5月</label>
                            <label><input type="checkbox" value="6"> 6月</label>
                            <label><input type="checkbox" value="7"> 7月</label>
                            <label><input type="checkbox" value="8"> 8月</label>
                            <label><input type="checkbox" value="9"> 9月</label>
                            <label><input type="checkbox" value="10"> 10月</label>
                            <label><input type="checkbox" value="11"> 11月</label>
                            <label><input type="checkbox" value="12"> 12月</label>
                        </div>
                    </div>
                    <button id="query-seasonal" class="btn-primary">分析季节性</button>
                </div>
                
                <div class="results">
                    <div id="seasonal-loading" class="loading hidden">分析中...</div>
                    <div id="seasonal-error" class="error hidden"></div>
                    <div id="seasonal-results" class="hidden">
                        <h3>季节性分析结果</h3>
                        <div class="chart-container">
                            <canvas id="seasonal-chart"></canvas>
                        </div>
                        <div class="stats-container">
                            <div id="seasonal-stats"></div>
                        </div>
                        <button id="export-seasonal" class="btn-secondary">导出结果</button>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- JavaScript文件 -->
    <script src="static/lib/chart.js"></script>
    <script src="static/js/utils.js"></script>
    <script src="static/js/api.js"></script>
    <script src="static/js/main.js"></script>
</body>
</html>
