#!/usr/bin/env python3
"""
股票板块数据分析系统 - 真实数据演示
使用真实历史数据展示系统功能
"""
import sys
import os
from pathlib import Path
import json
from datetime import datetime

# 添加backend目录到路径
sys.path.append('backend')

def verify_real_data():
    """验证真实数据的存在和完整性"""
    print("=" * 80)
    print("🔍 验证真实历史数据")
    print("=" * 80)
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return False
    
    # 获取所有CSV文件
    csv_files = list(data_dir.glob("*.csv"))
    csv_files.sort()
    
    print(f"✅ 发现 {len(csv_files)} 个真实数据文件")
    print(f"📅 数据时间范围: {csv_files[0].name} 至 {csv_files[-1].name}")
    
    # 验证板块代码文件
    sector_file = Path("sector_codes.json")
    if sector_file.exists():
        with open(sector_file, 'r', encoding='utf-8') as f:
            sector_data = json.load(f)
        sectors = sector_data.get("申万一级行业", {})
        print(f"✅ 板块分类数据: {len(sectors)} 个申万一级行业")
    else:
        print("❌ 板块代码文件不存在")
        return False
    
    # 验证最新数据文件的内容
    latest_file = csv_files[-1]
    print(f"\n📋 验证最新数据文件: {latest_file.name}")
    
    try:
        import pandas as pd
        df = pd.read_csv(latest_file)
        print(f"✅ 数据记录数: {len(df)} 条")
        print(f"✅ 数据字段: {list(df.columns)}")
        
        # 显示前3条真实数据
        print(f"\n📊 真实数据样本 (来自 {latest_file.name}):")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"  {i+1}. {row['sector_name']} ({row['sector_code']})")
            print(f"     开盘: {row['open']}, 收盘: {row['close']}, 涨跌幅: {row['change_pct']}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")
        return False

def run_real_analysis():
    """运行真实数据分析"""
    print("\n" + "=" * 80)
    print("🚀 启动真实数据分析引擎")
    print("=" * 80)
    
    try:
        from services.analysis_engine import AnalysisEngine
        from services.data_loader import SectorDataLoader
        
        # 初始化分析引擎
        print("🔧 初始化分析引擎...")
        engine = AnalysisEngine()
        
        # 获取可用的真实数据日期
        available_dates = engine.data_loader.get_available_dates()
        print(f"✅ 可用交易日数量: {len(available_dates)}")
        
        if len(available_dates) < 30:
            print("❌ 可用数据不足，无法进行有效分析")
            return False
        
        # 选择真实的分析时间段 - 使用2023年7月的真实数据
        print(f"\n📅 选择分析时间段...")
        
        # 寻找2023年7月的真实交易日
        july_2023_dates = []
        for date_str in available_dates:
            if date_str.startswith('202307'):  # 2023年7月
                july_2023_dates.append(date_str)
        
        if len(july_2023_dates) < 2:
            # 如果没有2023年7月数据，使用最近30天的数据
            print("⚠️ 2023年7月数据不足，使用最近30个交易日数据")
            recent_dates = available_dates[-30:]
            start_date_str = recent_dates[0]
            end_date_str = recent_dates[-1]
        else:
            start_date_str = july_2023_dates[0]
            end_date_str = july_2023_dates[-1]
        
        # 转换为标准日期格式
        start_date = datetime.strptime(start_date_str, '%Y%m%d').strftime('%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y%m%d').strftime('%Y-%m-%d')
        
        print(f"📊 分析时间段: {start_date} 至 {end_date}")
        print(f"📁 使用数据文件: {start_date_str}.csv 至 {end_date_str}.csv")
        
        # 验证数据文件存在
        start_file = Path(f"data/{start_date_str}.csv")
        end_file = Path(f"data/{end_date_str}.csv")
        
        if not start_file.exists() or not end_file.exists():
            print(f"❌ 数据文件不存在: {start_file} 或 {end_file}")
            return False
        
        print(f"✅ 确认使用真实数据文件:")
        print(f"   - 开始: {start_file}")
        print(f"   - 结束: {end_file}")
        
        return start_date, end_date, start_date_str, end_date_str, engine
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def perform_real_analysis(start_date, end_date, start_date_str, end_date_str, engine):
    """执行真实数据分析"""
    print(f"\n" + "=" * 80)
    print("📈 执行真实板块涨幅分析")
    print("=" * 80)
    
    try:
        # 1. 计算所有板块的真实涨幅
        print(f"🔄 计算期间涨幅: {start_date} 至 {end_date}")
        print("📊 使用真实历史交易数据...")
        
        period_returns = engine.calculate_period_returns(
            start_date=start_date,
            end_date=end_date
        )
        
        if not period_returns:
            print("❌ 无法计算涨幅数据")
            return False
        
        print(f"✅ 成功计算 {len(period_returns)} 个板块的真实涨幅")
        
        # 2. 显示数据来源验证
        print(f"\n🔍 数据来源验证:")
        
        # 读取并显示实际使用的数据文件内容
        import pandas as pd
        start_df = pd.read_csv(f"data/{start_date_str}.csv")
        end_df = pd.read_csv(f"data/{end_date_str}.csv")
        
        print(f"📁 开始日期数据文件: {start_date_str}.csv ({len(start_df)} 条记录)")
        print(f"📁 结束日期数据文件: {end_date_str}.csv ({len(end_df)} 条记录)")
        
        # 3. 获取排名前10的板块
        print(f"\n🏆 真实涨幅排名 TOP 10:")
        top_sectors = engine.get_top_sectors(period_returns, top_n=10)
        
        if not top_sectors:
            print("❌ 无法获取排名数据")
            return False
        
        print(f"{'排名':<4} {'板块名称':<12} {'板块代码':<8} {'期间涨幅':<8} {'开始价格':<8} {'结束价格':<8}")
        print("-" * 70)
        
        for sector in top_sectors:
            print(f"{sector['rank']:<4} {sector['sector_name']:<12} {sector['sector_code']:<8} "
                  f"{sector['period_return']:>7.2f}% {sector['start_price']:>8.2f} {sector['end_price']:>8.2f}")
        
        # 4. 显示具体的真实数据验证
        print(f"\n🔍 真实数据验证 (前3名板块):")
        for i, sector in enumerate(top_sectors[:3]):
            sector_code = sector['sector_code']
            
            # 从原始数据文件中查找对应记录
            start_record = start_df[start_df['sector_code'] == sector_code].iloc[0]
            end_record = end_df[end_df['sector_code'] == sector_code].iloc[0]
            
            print(f"\n{i+1}. {sector['sector_name']} ({sector_code}):")
            print(f"   📅 {start_date}: 收盘价 {start_record['close']} (来自真实数据)")
            print(f"   📅 {end_date}: 收盘价 {end_record['close']} (来自真实数据)")
            print(f"   📊 计算涨幅: ({end_record['close']} - {start_record['close']}) / {start_record['close']} × 100 = {sector['period_return']:.2f}%")
        
        # 5. 生成分析摘要
        print(f"\n📊 真实数据分析摘要:")
        summary = engine.get_analysis_summary(period_returns)
        
        print(f"   📈 分析板块总数: {summary['total_sectors']}")
        print(f"   ✅ 成功计算板块: {summary['success_sectors']}")
        print(f"   📊 成功率: {summary['success_rate']:.1f}%")
        print(f"   📈 平均涨幅: {summary['average_return']:.2f}%")
        print(f"   🔝 最大涨幅: {summary['max_return']:.2f}%")
        print(f"   🔻 最小涨幅: {summary['min_return']:.2f}%")
        print(f"   🎯 上涨板块数: {summary['positive_sectors']}")
        print(f"   📉 下跌板块数: {summary['negative_sectors']}")
        print(f"   🏆 胜率: {summary['win_rate']:.1f}%")
        
        # 6. 筛选示例 - 显示涨幅超过5%的板块
        print(f"\n🔍 筛选示例 - 涨幅超过5%的板块:")
        filter_criteria = {
            'status': 'success',
            'min_return': 5.0
        }
        
        filtered_results = engine.sector_filtering(period_returns, filter_criteria)
        
        if filtered_results:
            print(f"✅ 找到 {len(filtered_results)} 个涨幅超过5%的板块:")
            for sector_code, data in list(filtered_results.items())[:5]:  # 显示前5个
                print(f"   - {data['sector_name']}: {data['period_return']:.2f}%")
        else:
            print("   📊 该时间段内没有涨幅超过5%的板块")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主演示函数"""
    print("🚀 股票板块数据分析系统 - 真实数据演示")
    print("📊 使用真实历史数据进行板块涨幅排名分析")
    
    # 1. 验证真实数据
    if not verify_real_data():
        print("\n❌ 真实数据验证失败，无法继续演示")
        return False
    
    # 2. 初始化分析引擎
    analysis_result = run_real_analysis()
    if not analysis_result:
        print("\n❌ 分析引擎初始化失败")
        return False
    
    start_date, end_date, start_date_str, end_date_str, engine = analysis_result
    
    # 3. 执行真实数据分析
    if not perform_real_analysis(start_date, end_date, start_date_str, end_date_str, engine):
        print("\n❌ 真实数据分析失败")
        return False
    
    print(f"\n" + "=" * 80)
    print("🎉 真实数据分析演示完成！")
    print("=" * 80)
    print("✅ 所有结果均基于真实的股票板块历史交易数据")
    print("✅ 涨幅计算使用实际的开盘价、收盘价数据")
    print("✅ 排名结果反映真实的市场表现")
    print("✅ 统计指标基于真实的历史数据计算")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎯 系统已准备就绪，可以处理任何时间段的真实数据分析！")
        else:
            print(f"\n⚠️ 演示过程中遇到问题，请检查数据和环境配置")
    except KeyboardInterrupt:
        print(f"\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
