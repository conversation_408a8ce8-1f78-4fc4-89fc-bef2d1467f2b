#!/usr/bin/env python3
"""
简化版真实数据演示 - 直接分析CSV文件
"""
import csv
import json
import os
from pathlib import Path
from datetime import datetime

def read_csv_file(file_path):
    """读取CSV文件"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
        return data
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
        return None

def load_sector_codes():
    """加载板块代码"""
    try:
        with open('sector_codes.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data.get('申万一级行业', {})
    except Exception as e:
        print(f"加载板块代码失败: {e}")
        return {}

def calculate_real_returns(start_file, end_file):
    """计算真实涨幅"""
    print(f"📊 读取真实数据文件:")
    print(f"   开始: {start_file}")
    print(f"   结束: {end_file}")
    
    start_data = read_csv_file(start_file)
    end_data = read_csv_file(end_file)
    
    if not start_data or not end_data:
        return None
    
    print(f"✅ 开始日期数据: {len(start_data)} 条记录")
    print(f"✅ 结束日期数据: {len(end_data)} 条记录")
    
    # 创建字典便于查找
    start_dict = {row['sector_code']: row for row in start_data}
    end_dict = {row['sector_code']: row for row in end_data}
    
    # 计算涨幅
    results = []
    sector_codes = load_sector_codes()
    
    for sector_code in start_dict:
        if sector_code in end_dict:
            try:
                start_price = float(start_dict[sector_code]['close'])
                end_price = float(end_dict[sector_code]['close'])
                
                if start_price > 0:
                    period_return = ((end_price - start_price) / start_price) * 100
                    
                    results.append({
                        'sector_code': sector_code,
                        'sector_name': sector_codes.get(sector_code, sector_code),
                        'start_price': start_price,
                        'end_price': end_price,
                        'period_return': period_return
                    })
            except (ValueError, ZeroDivisionError):
                continue
    
    return results

def main():
    """主演示函数"""
    print("=" * 80)
    print("🚀 股票板块数据分析系统 - 真实数据演示")
    print("=" * 80)
    
    # 1. 检查数据目录
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return
    
    csv_files = list(data_dir.glob("*.csv"))
    csv_files.sort()
    
    if len(csv_files) < 2:
        print("❌ 数据文件不足")
        return
    
    print(f"✅ 发现 {len(csv_files)} 个真实数据文件")
    print(f"📅 数据范围: {csv_files[0].name} 至 {csv_files[-1].name}")
    
    # 2. 选择分析时间段 - 使用最近的数据
    if len(csv_files) >= 30:
        # 使用最近30天的数据中的第一天和最后一天
        start_file = csv_files[-30]
        end_file = csv_files[-1]
    else:
        # 使用第一个和最后一个文件
        start_file = csv_files[0]
        end_file = csv_files[-1]
    
    start_date = start_file.stem
    end_date = end_file.stem
    
    print(f"\n📊 选择分析时间段:")
    print(f"   开始日期: {start_date}")
    print(f"   结束日期: {end_date}")
    
    # 3. 验证真实数据内容
    print(f"\n🔍 验证真实数据内容:")
    
    # 读取并显示最新文件的样本数据
    latest_data = read_csv_file(end_file)
    if latest_data and len(latest_data) > 0:
        print(f"✅ 最新数据文件 {end_file.name} 包含 {len(latest_data)} 条真实记录")
        print(f"📋 数据字段: {list(latest_data[0].keys())}")
        
        print(f"\n📊 真实数据样本 (来自 {end_file.name}):")
        for i in range(min(3, len(latest_data))):
            row = latest_data[i]
            print(f"  {i+1}. {row.get('sector_name', 'N/A')} ({row.get('sector_code', 'N/A')})")
            print(f"     开盘: {row.get('open', 'N/A')}, 收盘: {row.get('close', 'N/A')}, 涨跌幅: {row.get('change_pct', 'N/A')}%")
    
    # 4. 计算真实涨幅
    print(f"\n📈 计算真实板块涨幅:")
    results = calculate_real_returns(start_file, end_file)
    
    if not results:
        print("❌ 无法计算涨幅数据")
        return
    
    print(f"✅ 成功计算 {len(results)} 个板块的真实涨幅")
    
    # 5. 排序并显示结果
    results.sort(key=lambda x: x['period_return'], reverse=True)
    
    print(f"\n🏆 真实涨幅排名 TOP 10:")
    print(f"{'排名':<4} {'板块名称':<15} {'板块代码':<10} {'涨幅%':<8} {'开始价格':<10} {'结束价格':<10}")
    print("-" * 80)
    
    for i, result in enumerate(results[:10]):
        print(f"{i+1:<4} {result['sector_name']:<15} {result['sector_code']:<10} "
              f"{result['period_return']:>6.2f}% {result['start_price']:>9.2f} {result['end_price']:>9.2f}")
    
    # 6. 数据验证 - 显示计算过程
    print(f"\n🔍 真实数据计算验证 (前3名):")
    for i, result in enumerate(results[:3]):
        print(f"\n{i+1}. {result['sector_name']} ({result['sector_code']}):")
        print(f"   📅 {start_date}: 收盘价 {result['start_price']:.2f} (来自真实CSV数据)")
        print(f"   📅 {end_date}: 收盘价 {result['end_price']:.2f} (来自真实CSV数据)")
        print(f"   📊 涨幅计算: ({result['end_price']:.2f} - {result['start_price']:.2f}) / {result['start_price']:.2f} × 100 = {result['period_return']:.2f}%")
    
    # 7. 统计分析
    print(f"\n📊 真实数据统计分析:")
    
    total_sectors = len(results)
    positive_sectors = len([r for r in results if r['period_return'] > 0])
    negative_sectors = len([r for r in results if r['period_return'] < 0])
    zero_sectors = total_sectors - positive_sectors - negative_sectors
    
    avg_return = sum(r['period_return'] for r in results) / total_sectors
    max_return = max(r['period_return'] for r in results)
    min_return = min(r['period_return'] for r in results)
    win_rate = (positive_sectors / total_sectors) * 100
    
    print(f"   📈 分析板块总数: {total_sectors}")
    print(f"   📊 平均涨幅: {avg_return:.2f}%")
    print(f"   🔝 最大涨幅: {max_return:.2f}%")
    print(f"   🔻 最小涨幅: {min_return:.2f}%")
    print(f"   🎯 上涨板块: {positive_sectors} 个")
    print(f"   📉 下跌板块: {negative_sectors} 个")
    print(f"   ➖ 平盘板块: {zero_sectors} 个")
    print(f"   🏆 胜率: {win_rate:.1f}%")
    
    # 8. 筛选示例
    high_return_sectors = [r for r in results if r['period_return'] > 5.0]
    
    print(f"\n🔍 筛选示例 - 涨幅超过5%的板块:")
    if high_return_sectors:
        print(f"✅ 找到 {len(high_return_sectors)} 个涨幅超过5%的板块:")
        for result in high_return_sectors[:5]:
            print(f"   - {result['sector_name']}: {result['period_return']:.2f}%")
    else:
        print("   📊 该时间段内没有涨幅超过5%的板块")
    
    print(f"\n" + "=" * 80)
    print("🎉 真实数据分析演示完成！")
    print("=" * 80)
    print("✅ 所有结果均基于真实的股票板块历史交易数据")
    print("✅ 数据来源: data/目录下的真实CSV文件")
    print("✅ 涨幅计算使用实际的收盘价数据")
    print("✅ 排名结果反映真实的市场表现")
    print(f"✅ 分析时间段: {start_date} 至 {end_date}")

if __name__ == "__main__":
    main()
