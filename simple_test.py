#!/usr/bin/env python3
"""
简单测试数据加载服务的基本功能
"""
import json
import os
from pathlib import Path

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("基本功能测试")
    print("=" * 50)
    
    # 1. 测试文件存在性
    print("\n1. 检查关键文件...")
    
    files_to_check = [
        "backend/services/data_loader.py",
        "sector_codes.json",
        "data"
    ]
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
    
    # 2. 测试板块代码文件
    print("\n2. 测试板块代码文件...")
    try:
        with open("sector_codes.json", 'r', encoding='utf-8') as f:
            sector_data = json.load(f)
        
        if "申万一级行业" in sector_data:
            sectors = sector_data["申万一级行业"]
            print(f"✓ 板块代码加载成功，数量: {len(sectors)}")
            
            # 显示前3个
            sample = list(sectors.items())[:3]
            for code, name in sample:
                print(f"  - {code}: {name}")
        else:
            print("✗ 板块代码格式不正确")
    except Exception as e:
        print(f"✗ 板块代码加载失败: {e}")
    
    # 3. 测试数据文件
    print("\n3. 测试数据文件...")
    data_dir = Path("data")
    if data_dir.exists():
        csv_files = list(data_dir.glob("*.csv"))
        print(f"✓ 数据目录存在，CSV文件数量: {len(csv_files)}")
        
        if csv_files:
            # 检查最新的几个文件
            csv_files.sort()
            recent_files = csv_files[-3:]
            print("  最近的文件:")
            for file in recent_files:
                print(f"  - {file.name}")
                
            # 检查一个文件的内容
            sample_file = recent_files[-1]
            try:
                with open(sample_file, 'r', encoding='utf-8') as f:
                    header = f.readline().strip()
                    first_line = f.readline().strip()
                
                print(f"  样本文件: {sample_file.name}")
                print(f"  表头: {header}")
                print(f"  首行数据: {first_line[:100]}...")
                
                # 验证列数
                columns = header.split(',')
                data_fields = first_line.split(',')
                print(f"  列数: {len(columns)}, 数据字段数: {len(data_fields)}")
                
            except Exception as e:
                print(f"  ✗ 读取样本文件失败: {e}")
    else:
        print("✗ 数据目录不存在")
    
    # 4. 测试数据加载服务代码
    print("\n4. 检查数据加载服务代码...")
    loader_file = Path("backend/services/data_loader.py")
    if loader_file.exists():
        with open(loader_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类和方法
        key_elements = [
            "class SectorDataLoader",
            "def load_date_data",
            "def load_date_range", 
            "def get_available_dates",
            "def _validate_csv_data"
        ]
        
        for element in key_elements:
            if element in content:
                print(f"✓ {element}")
            else:
                print(f"✗ {element}")
        
        print(f"✓ 代码文件大小: {len(content)} 字符")
    else:
        print("✗ 数据加载服务文件不存在")
    
    print("\n" + "=" * 50)
    print("基本功能检查完成")
    print("=" * 50)

if __name__ == "__main__":
    test_basic_functionality()
