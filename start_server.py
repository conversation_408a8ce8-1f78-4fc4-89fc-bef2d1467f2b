#!/usr/bin/env python3
"""
启动股票板块数据分析系统服务器
"""
import sys
import os
import subprocess
import json
from pathlib import Path

def check_python_environment():
    """检查Python环境"""
    print("=" * 60)
    print("检查Python环境")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查基础包
    basic_packages = ['json', 'os', 'pathlib', 'subprocess']
    for package in basic_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("\n" + "=" * 60)
    print("检查项目结构")
    print("=" * 60)
    
    required_files = [
        "backend/main.py",
        "sector_codes.json",
        "frontend/index.html",
        "requirements.txt"
    ]
    
    all_exist = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            all_exist = False
    
    # 检查数据目录
    data_dir = Path("data")
    if data_dir.exists():
        csv_files = list(data_dir.glob("*.csv"))
        print(f"✓ data/ 目录存在，包含 {len(csv_files)} 个CSV文件")
    else:
        print("✗ data/ 目录不存在")
        all_exist = False
    
    return all_exist

def check_dependencies():
    """检查依赖包"""
    print("\n" + "=" * 60)
    print("检查依赖包")
    print("=" * 60)
    
    dependencies = {
        'fastapi': 'FastAPI Web框架',
        'uvicorn': 'ASGI服务器',
        'pandas': '数据处理库',
        'requests': 'HTTP请求库'
    }
    
    available_deps = {}
    for package, description in dependencies.items():
        try:
            __import__(package)
            print(f"✓ {package} - {description}")
            available_deps[package] = True
        except ImportError:
            print(f"✗ {package} - {description} (未安装)")
            available_deps[package] = False
    
    return available_deps

def install_dependencies():
    """尝试安装依赖"""
    print("\n" + "=" * 60)
    print("尝试安装依赖")
    print("=" * 60)
    
    # 尝试不同的pip命令
    pip_commands = [
        [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
        ['pip', 'install', '-r', 'requirements.txt'],
        ['pip3', 'install', '-r', 'requirements.txt'],
        ['py', '-m', 'pip', 'install', '-r', 'requirements.txt']
    ]
    
    for cmd in pip_commands:
        try:
            print(f"尝试命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✓ 依赖安装成功！")
                return True
            else:
                print(f"✗ 安装失败: {result.stderr}")
        except subprocess.TimeoutExpired:
            print("✗ 安装超时")
        except FileNotFoundError:
            print(f"✗ 命令不存在: {cmd[0]}")
        except Exception as e:
            print(f"✗ 安装出错: {e}")
    
    print("⚠ 自动安装失败，请手动安装依赖")
    return False

def start_server_simple():
    """启动简化版服务器（不依赖FastAPI）"""
    print("\n" + "=" * 60)
    print("启动简化版HTTP服务器")
    print("=" * 60)
    
    try:
        import http.server
        import socketserver
        import webbrowser
        
        PORT = 8000
        
        class CustomHandler(http.server.SimpleHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    
                    html_content = """
                    <!DOCTYPE html>
                    <html lang="zh-CN">
                    <head>
                        <meta charset="UTF-8">
                        <title>股票板块数据分析系统</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 40px; }
                            .header { background: #f0f8ff; padding: 20px; border-radius: 8px; }
                            .info { margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px; }
                            .status { color: green; font-weight: bold; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>🚀 股票板块数据分析系统</h1>
                            <p class="status">系统状态: 运行中</p>
                        </div>
                        
                        <div class="info">
                            <h3>系统信息</h3>
                            <ul>
                                <li>版本: 1.0.0</li>
                                <li>服务器: Python HTTP Server</li>
                                <li>端口: 8000</li>
                                <li>状态: 简化模式运行</li>
                            </ul>
                        </div>
                        
                        <div class="info">
                            <h3>功能特性</h3>
                            <ul>
                                <li>✓ 项目结构已建立</li>
                                <li>✓ 数据加载服务已实现</li>
                                <li>⏳ FastAPI服务器（需要安装依赖）</li>
                                <li>⏳ 数据分析功能（开发中）</li>
                                <li>⏳ 前端界面（开发中）</li>
                            </ul>
                        </div>
                        
                        <div class="info">
                            <h3>下一步</h3>
                            <p>1. 安装依赖: <code>pip install fastapi uvicorn pandas</code></p>
                            <p>2. 启动FastAPI服务器: <code>uvicorn backend.main:app --reload</code></p>
                            <p>3. 访问完整功能界面</p>
                        </div>
                    </body>
                    </html>
                    """
                    
                    self.wfile.write(html_content.encode('utf-8'))
                    
                elif self.path == '/health':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    
                    health_data = {
                        "status": "healthy",
                        "mode": "simple_http_server",
                        "message": "系统基础功能正常"
                    }
                    
                    self.wfile.write(json.dumps(health_data, ensure_ascii=False).encode('utf-8'))
                else:
                    super().do_GET()
        
        with socketserver.TCPServer(("", PORT), CustomHandler) as httpd:
            print(f"✓ 简化版服务器启动成功")
            print(f"✓ 访问地址: http://localhost:{PORT}")
            print(f"✓ 健康检查: http://localhost:{PORT}/health")
            print("✓ 按 Ctrl+C 停止服务器")
            
            # 尝试打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("✓ 已尝试打开浏览器")
            except:
                pass
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n✓ 服务器已停止")
    except Exception as e:
        print(f"✗ 启动简化服务器失败: {e}")

def start_fastapi_server():
    """启动FastAPI服务器"""
    print("\n" + "=" * 60)
    print("启动FastAPI服务器")
    print("=" * 60)
    
    try:
        # 尝试导入FastAPI
        import fastapi
        import uvicorn
        
        print("✓ FastAPI和uvicorn可用")
        
        # 启动服务器
        os.chdir(Path(__file__).parent)
        
        print("启动命令: uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000")
        
        # 使用subprocess启动，这样可以看到输出
        cmd = [sys.executable, '-m', 'uvicorn', 'backend.main:app', '--reload', '--host', '0.0.0.0', '--port', '8000']
        subprocess.run(cmd)
        
    except ImportError as e:
        print(f"✗ FastAPI或uvicorn未安装: {e}")
        print("回退到简化版服务器...")
        start_server_simple()
    except Exception as e:
        print(f"✗ 启动FastAPI服务器失败: {e}")
        print("回退到简化版服务器...")
        start_server_simple()

def main():
    """主函数"""
    print("股票板块数据分析系统 - 服务器启动脚本")
    
    # 1. 检查Python环境
    check_python_environment()
    
    # 2. 检查项目结构
    if not check_project_structure():
        print("\n❌ 项目结构不完整，请检查文件")
        return
    
    # 3. 检查依赖
    deps = check_dependencies()
    
    # 4. 如果FastAPI可用，启动FastAPI服务器，否则启动简化服务器
    if deps.get('fastapi', False) and deps.get('uvicorn', False):
        start_fastapi_server()
    else:
        print("\n⚠ FastAPI未安装，尝试自动安装...")
        if install_dependencies():
            start_fastapi_server()
        else:
            print("\n启动简化版服务器进行演示...")
            start_server_simple()

if __name__ == "__main__":
    main()
