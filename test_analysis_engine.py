#!/usr/bin/env python3
"""
测试涨幅排名分析引擎
"""
import sys
import os
from pathlib import Path

# 添加backend目录到路径
sys.path.append('backend')

def test_analysis_engine():
    """测试分析引擎的各项功能"""
    print("=" * 70)
    print("涨幅排名分析引擎测试")
    print("=" * 70)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from services.analysis_engine import AnalysisEngine
        from services.data_loader import SectorDataLoader
        from utils.calculation import calculate_return_rate, calculate_win_rate
        print("✓ 模块导入成功")
        
        # 2. 初始化分析引擎
        print("\n2. 初始化分析引擎...")
        engine = AnalysisEngine()
        print("✓ 分析引擎初始化成功")
        
        # 3. 测试日期范围验证
        print("\n3. 测试日期范围验证...")
        validation_result = engine.validate_date_range('2023-07-01', '2023-07-31')
        print(f"✓ 日期验证结果: {validation_result}")
        
        # 4. 获取可用板块
        print("\n4. 获取可用板块...")
        sectors = engine.get_available_sectors()
        print(f"✓ 可用板块数量: {len(sectors)}")
        
        # 显示前5个板块
        sample_sectors = list(sectors.items())[:5]
        for code, name in sample_sectors:
            print(f"  - {code}: {name}")
        
        # 5. 测试期间涨幅计算
        print("\n5. 测试期间涨幅计算...")
        
        # 选择几个板块进行测试
        test_sectors = list(sectors.keys())[:3]  # 取前3个板块
        print(f"测试板块: {test_sectors}")
        
        period_returns = engine.calculate_period_returns(
            start_date='2023-07-01',
            end_date='2023-07-31',
            sectors=test_sectors
        )
        
        print(f"✓ 成功计算 {len(period_returns)} 个板块的涨幅")
        
        # 显示计算结果
        for sector_code, data in period_returns.items():
            if data['status'] == 'success':
                print(f"  - {sector_code} ({data['sector_name']}): {data['period_return']}%")
            else:
                print(f"  - {sector_code}: 计算失败 - {data['error_message']}")
        
        # 6. 测试排名功能
        print("\n6. 测试排名功能...")
        top_sectors = engine.get_top_sectors(period_returns, top_n=3)
        
        print(f"✓ 涨幅排名前3名:")
        for sector in top_sectors:
            print(f"  {sector['rank']}. {sector['sector_name']} ({sector['sector_code']}): {sector['period_return']}%")
        
        # 7. 测试分析摘要
        print("\n7. 测试分析摘要...")
        summary = engine.get_analysis_summary(period_returns)
        print(f"✓ 分析摘要:")
        print(f"  - 总板块数: {summary['total_sectors']}")
        print(f"  - 成功计算: {summary['success_sectors']}")
        print(f"  - 平均涨幅: {summary['average_return']}%")
        print(f"  - 最大涨幅: {summary['max_return']}%")
        print(f"  - 最小涨幅: {summary['min_return']}%")
        print(f"  - 胜率: {summary['win_rate']}%")
        
        # 8. 测试板块筛选
        print("\n8. 测试板块筛选...")
        filter_criteria = {
            'status': 'success',
            'min_return': 0  # 只要正收益的板块
        }
        
        filtered_results = engine.sector_filtering(period_returns, filter_criteria)
        print(f"✓ 筛选结果: {len(filtered_results)} 个正收益板块")
        
        for sector_code, data in filtered_results.items():
            print(f"  - {data['sector_name']}: {data['period_return']}%")
        
        # 9. 测试计算工具函数
        print("\n9. 测试计算工具函数...")
        
        # 测试收益率计算
        test_return = calculate_return_rate(100, 110)
        print(f"✓ 收益率计算 (100->110): {test_return}%")
        
        # 测试胜率计算
        test_returns = [5.2, -2.1, 3.8, -1.5, 7.3]
        win_stats = calculate_win_rate(test_returns)
        print(f"✓ 胜率统计: {win_stats['win_rate']}% ({win_stats['positive_count']}/{win_stats['total_count']})")
        
        print("\n" + "=" * 70)
        print("✅ 所有测试通过！涨幅排名分析引擎工作正常。")
        print("=" * 70)
        return True
        
    except ImportError as e:
        print(f"\n❌ 模块导入失败: {e}")
        print("请确保项目结构正确，并且所有依赖文件存在")
        return False
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 70)
    print("边界情况测试")
    print("=" * 70)
    
    try:
        from services.analysis_engine import AnalysisEngine
        engine = AnalysisEngine()
        
        # 1. 测试无效日期范围
        print("\n1. 测试无效日期范围...")
        invalid_validation = engine.validate_date_range('2023-07-31', '2023-07-01')
        print(f"✓ 无效日期范围验证: {invalid_validation['valid']} - {invalid_validation.get('error', '')}")
        
        # 2. 测试不存在的日期
        print("\n2. 测试不存在的日期...")
        future_returns = engine.calculate_period_returns('2030-01-01', '2030-01-31')
        print(f"✓ 未来日期处理: 返回 {len(future_returns)} 个结果")
        
        # 3. 测试空板块列表
        print("\n3. 测试空板块列表...")
        empty_returns = engine.calculate_period_returns(
            '2023-07-01', '2023-07-31', sectors=[]
        )
        print(f"✓ 空板块列表处理: 返回 {len(empty_returns)} 个结果")
        
        # 4. 测试无效板块代码
        print("\n4. 测试无效板块代码...")
        invalid_returns = engine.calculate_period_returns(
            '2023-07-01', '2023-07-31', sectors=['INVALID_CODE']
        )
        print(f"✓ 无效板块代码处理: 返回 {len(invalid_returns)} 个结果")
        
        print("\n✅ 边界情况测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 边界情况测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n" + "=" * 70)
    print("性能测试")
    print("=" * 70)
    
    try:
        from services.analysis_engine import AnalysisEngine
        import time
        
        engine = AnalysisEngine()
        
        # 获取所有板块
        all_sectors = list(engine.get_available_sectors().keys())
        
        if len(all_sectors) > 10:
            test_sectors = all_sectors[:10]  # 测试前10个板块
        else:
            test_sectors = all_sectors
        
        print(f"性能测试：分析 {len(test_sectors)} 个板块")
        
        # 测试计算性能
        start_time = time.time()
        
        period_returns = engine.calculate_period_returns(
            start_date='2023-07-01',
            end_date='2023-07-31',
            sectors=test_sectors
        )
        
        calculation_time = time.time() - start_time
        
        # 测试排名性能
        start_time = time.time()
        top_sectors = engine.get_top_sectors(period_returns, top_n=5)
        ranking_time = time.time() - start_time
        
        # 测试摘要性能
        start_time = time.time()
        summary = engine.get_analysis_summary(period_returns)
        summary_time = time.time() - start_time
        
        print(f"✓ 性能统计:")
        print(f"  - 涨幅计算: {calculation_time:.3f} 秒 ({len(test_sectors)} 个板块)")
        print(f"  - 排名计算: {ranking_time:.3f} 秒")
        print(f"  - 摘要生成: {summary_time:.3f} 秒")
        print(f"  - 总耗时: {calculation_time + ranking_time + summary_time:.3f} 秒")
        print(f"  - 平均每板块: {calculation_time / len(test_sectors):.4f} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("涨幅排名分析引擎 - 综合测试")
    
    # 检查项目结构
    required_files = [
        'backend/services/analysis_engine.py',
        'backend/services/data_loader.py',
        'backend/utils/calculation.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 执行测试
    tests = [
        ("基本功能测试", test_analysis_engine),
        ("边界情况测试", test_edge_cases),
        ("性能测试", test_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 70)
    print("测试结果汇总:")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！分析引擎已准备就绪。")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
