#!/usr/bin/env python3
"""
测试数据加载服务
"""
import sys
import os
sys.path.append('backend')

from services.data_loader import SectorDataLoader
import pandas as pd
from datetime import datetime

def test_data_loader():
    """测试数据加载服务的各项功能"""
    print("=" * 60)
    print("数据加载服务测试")
    print("=" * 60)
    
    try:
        # 1. 初始化数据加载器
        print("\n1. 初始化数据加载器...")
        loader = SectorDataLoader()
        print("✓ 数据加载器初始化成功")
        
        # 2. 测试板块信息
        print("\n2. 测试板块信息...")
        sector_info = loader.get_sector_info()
        print(f"✓ 板块数量: {len(sector_info)}")
        
        # 显示前5个板块
        sample_sectors = list(sector_info.items())[:5]
        for code, name in sample_sectors:
            print(f"  - {code}: {name}")
        
        # 3. 测试可用日期
        print("\n3. 测试可用日期...")
        available_dates = loader.get_available_dates()
        print(f"✓ 可用交易日数量: {len(available_dates)}")
        
        if available_dates:
            print(f"  - 最早日期: {available_dates[0]}")
            print(f"  - 最新日期: {available_dates[-1]}")
            
            # 显示最近5个交易日
            recent_dates = available_dates[-5:]
            print(f"  - 最近5个交易日: {', '.join(recent_dates)}")
        
        # 4. 测试日期范围信息
        print("\n4. 测试日期范围信息...")
        date_range_info = loader.get_date_range_info()
        print(f"✓ 数据范围: {date_range_info['start_date']} 至 {date_range_info['end_date']}")
        print(f"✓ 总交易日数: {date_range_info['total_days']}")
        
        # 5. 测试单日数据加载
        print("\n5. 测试单日数据加载...")
        if available_dates:
            test_date = available_dates[-1]  # 使用最新日期
            df = loader.load_date_data(test_date)
            
            if df is not None:
                print(f"✓ 成功加载 {test_date} 数据")
                print(f"  - 数据行数: {len(df)}")
                print(f"  - 数据列数: {len(df.columns)}")
                print(f"  - 列名: {list(df.columns)}")
                
                # 显示前3行数据
                print("  - 前3行数据:")
                print(df.head(3).to_string(index=False))
            else:
                print(f"✗ 加载 {test_date} 数据失败")
        
        # 6. 测试日期范围数据加载
        print("\n6. 测试日期范围数据加载...")
        if len(available_dates) >= 3:
            # 测试最近3个交易日
            recent_dates = available_dates[-3:]
            start_date = datetime.strptime(recent_dates[0], '%Y%m%d').strftime('%Y-%m-%d')
            end_date = datetime.strptime(recent_dates[-1], '%Y%m%d').strftime('%Y-%m-%d')
            
            range_data = loader.load_date_range(start_date, end_date)
            print(f"✓ 成功加载日期范围 {start_date} 至 {end_date}")
            print(f"  - 加载天数: {len(range_data)}")
            
            for date_key, df in range_data.items():
                print(f"  - {date_key}: {len(df)} 条记录")
        
        # 7. 测试缓存功能
        print("\n7. 测试缓存功能...")
        cache_info = loader.get_cache_info()
        print(f"✓ 缓存状态:")
        print(f"  - 已缓存日期数: {cache_info['cache_size']}")
        print(f"  - 缓存限制: {cache_info['cache_limit']}")
        print(f"  - 已缓存日期: {', '.join(cache_info['cached_dates'][:5])}")
        
        # 8. 测试数据验证
        print("\n8. 测试数据验证...")
        if available_dates:
            test_date = available_dates[-1]
            exists = loader.validate_date_exists(test_date)
            print(f"✓ 日期 {test_date} 数据存在性验证: {exists}")
            
            # 测试不存在的日期
            fake_date = "20991231"
            exists = loader.validate_date_exists(fake_date)
            print(f"✓ 日期 {fake_date} 数据存在性验证: {exists}")
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！数据加载服务工作正常。")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """测试性能"""
    print("\n" + "=" * 60)
    print("性能测试")
    print("=" * 60)
    
    try:
        loader = SectorDataLoader()
        available_dates = loader.get_available_dates()
        
        if len(available_dates) >= 10:
            # 测试加载10个交易日的性能
            test_dates = available_dates[-10:]
            
            import time
            start_time = time.time()
            
            for date_str in test_dates:
                df = loader.load_date_data(date_str)
                if df is not None:
                    print(f"✓ 加载 {date_str}: {len(df)} 条记录")
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            print(f"\n性能统计:")
            print(f"  - 加载10个交易日用时: {elapsed:.2f} 秒")
            print(f"  - 平均每个交易日: {elapsed/10:.3f} 秒")
            
            # 测试缓存性能
            print(f"\n测试缓存性能...")
            start_time = time.time()
            
            # 重复加载相同数据（应该从缓存读取）
            for date_str in test_dates:
                df = loader.load_date_data(date_str)
            
            end_time = time.time()
            cached_elapsed = end_time - start_time
            
            print(f"  - 缓存加载10个交易日用时: {cached_elapsed:.3f} 秒")
            print(f"  - 性能提升: {elapsed/cached_elapsed:.1f}x")
            
    except Exception as e:
        print(f"性能测试失败: {e}")

if __name__ == "__main__":
    success = test_data_loader()
    if success:
        test_performance()
    
    sys.exit(0 if success else 1)
