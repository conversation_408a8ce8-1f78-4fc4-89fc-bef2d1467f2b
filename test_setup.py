#!/usr/bin/env python3
"""
测试项目设置和依赖
"""
import sys
import json
import os
from pathlib import Path

def test_imports():
    """测试必要的包导入"""
    print("测试包导入...")
    
    try:
        import pandas as pd
        print("✓ pandas 导入成功")
    except ImportError as e:
        print(f"✗ pandas 导入失败: {e}")
        return False
    
    try:
        import requests
        print("✓ requests 导入成功")
    except ImportError as e:
        print(f"✗ requests 导入失败: {e}")
        return False
    
    # 测试FastAPI（可能还未安装）
    try:
        import fastapi
        print("✓ FastAPI 导入成功")
    except ImportError as e:
        print(f"⚠ FastAPI 未安装，需要运行: pip install -r requirements.txt")
    
    return True

def test_sector_codes():
    """测试板块代码文件读取"""
    print("\n测试板块代码文件...")
    
    sector_file = Path("sector_codes.json")
    if not sector_file.exists():
        print(f"✗ 板块代码文件不存在: {sector_file}")
        return False
    
    try:
        with open(sector_file, 'r', encoding='utf-8') as f:
            sector_data = json.load(f)
        
        print(f"✓ 板块代码文件读取成功")
        
        # 检查数据结构
        if "申万一级行业" in sector_data:
            sectors = sector_data["申万一级行业"]
            print(f"✓ 找到申万一级行业板块: {len(sectors)} 个")
            
            # 显示前5个板块
            sample_sectors = list(sectors.items())[:5]
            for code, name in sample_sectors:
                print(f"  - {code}: {name}")
            
            return True
        else:
            print("✗ 板块代码文件格式不正确")
            return False
            
    except Exception as e:
        print(f"✗ 读取板块代码文件失败: {e}")
        return False

def test_data_directory():
    """测试数据目录"""
    print("\n测试数据目录...")
    
    data_dir = Path("data")
    if not data_dir.exists():
        print(f"✗ 数据目录不存在: {data_dir}")
        return False
    
    # 统计CSV文件数量
    csv_files = list(data_dir.glob("*.csv"))
    print(f"✓ 数据目录存在，包含 {len(csv_files)} 个CSV文件")
    
    if csv_files:
        # 测试读取一个CSV文件
        sample_file = csv_files[0]
        try:
            import pandas as pd
            df = pd.read_csv(sample_file)
            print(f"✓ 成功读取样本文件: {sample_file.name}")
            print(f"  - 数据行数: {len(df)}")
            print(f"  - 数据列: {list(df.columns)}")
            return True
        except Exception as e:
            print(f"✗ 读取CSV文件失败: {e}")
            return False
    
    return True

def test_project_structure():
    """测试项目结构"""
    print("\n测试项目结构...")
    
    required_dirs = [
        "backend",
        "backend/api",
        "backend/services", 
        "backend/models",
        "backend/utils",
        "frontend",
        "frontend/static",
        "frontend/static/css",
        "frontend/static/js",
        "frontend/static/lib"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✓ {dir_path}")
        else:
            print(f"✗ {dir_path}")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("=" * 50)
    print("项目设置验证")
    print("=" * 50)
    
    tests = [
        ("包导入测试", test_imports),
        ("板块代码文件测试", test_sector_codes),
        ("数据目录测试", test_data_directory),
        ("项目结构测试", test_project_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！项目设置完成。")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
