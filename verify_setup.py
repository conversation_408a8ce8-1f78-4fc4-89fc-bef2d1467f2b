#!/usr/bin/env python3
"""
验证项目设置的简单脚本
"""
import json
import os
from pathlib import Path

def main():
    print("=" * 50)
    print("项目设置验证")
    print("=" * 50)
    
    # 1. 检查项目结构
    print("\n1. 检查项目结构...")
    required_dirs = [
        "backend", "backend/api", "backend/services", 
        "backend/models", "backend/utils", "frontend",
        "frontend/static", "frontend/static/css",
        "frontend/static/js", "frontend/static/lib"
    ]
    
    structure_ok = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✓ {dir_path}")
        else:
            print(f"✗ {dir_path}")
            structure_ok = False
    
    # 2. 检查关键文件
    print("\n2. 检查关键文件...")
    required_files = [
        "backend/main.py", "config.py", "requirements.txt",
        "sector_codes.json", "frontend/index.html"
    ]
    
    files_ok = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            files_ok = False
    
    # 3. 检查板块代码文件
    print("\n3. 检查板块代码文件...")
    try:
        with open("sector_codes.json", 'r', encoding='utf-8') as f:
            sector_data = json.load(f)
        
        if "申万一级行业" in sector_data:
            sectors = sector_data["申万一级行业"]
            print(f"✓ 板块代码文件读取成功，包含 {len(sectors)} 个板块")
            
            # 显示前3个板块
            sample_sectors = list(sectors.items())[:3]
            for code, name in sample_sectors:
                print(f"  - {code}: {name}")
            sectors_ok = True
        else:
            print("✗ 板块代码文件格式不正确")
            sectors_ok = False
    except Exception as e:
        print(f"✗ 读取板块代码文件失败: {e}")
        sectors_ok = False
    
    # 4. 检查数据目录
    print("\n4. 检查数据目录...")
    data_dir = Path("data")
    if data_dir.exists():
        csv_files = list(data_dir.glob("*.csv"))
        print(f"✓ 数据目录存在，包含 {len(csv_files)} 个CSV文件")
        
        if csv_files:
            # 检查一个样本文件
            sample_file = csv_files[0]
            try:
                with open(sample_file, 'r', encoding='utf-8') as f:
                    header = f.readline().strip()
                    first_line = f.readline().strip()
                
                expected_columns = ['sector_code', 'sector_name', 'date', 'open', 'close', 'high', 'low', 'volume', 'amount', 'amplitude', 'change_pct', 'change_amount', 'turnover_rate']
                actual_columns = header.split(',')
                
                if actual_columns == expected_columns:
                    print(f"✓ CSV文件格式正确: {sample_file.name}")
                    data_ok = True
                else:
                    print(f"✗ CSV文件格式不正确: {sample_file.name}")
                    data_ok = False
            except Exception as e:
                print(f"✗ 读取CSV文件失败: {e}")
                data_ok = False
        else:
            print("⚠ 数据目录为空")
            data_ok = False
    else:
        print("✗ 数据目录不存在")
        data_ok = False
    
    # 5. 汇总结果
    print("\n" + "=" * 50)
    print("验证结果汇总:")
    print("=" * 50)
    
    results = [
        ("项目结构", structure_ok),
        ("关键文件", files_ok),
        ("板块代码", sectors_ok),
        ("数据文件", data_ok)
    ]
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项验证通过")
    
    if passed == len(results):
        print("\n🎉 项目设置验证通过！")
        print("✓ 项目结构创建完成")
        print("✓ 依赖配置文件更新完成")
        print("✓ 现有数据文件兼容")
        print("✓ 板块代码映射可用")
        print("\n下一步：安装依赖")
        print("pip install -r requirements.txt")
        return True
    else:
        print("\n⚠ 部分验证失败，请检查相关配置")
        return False

if __name__ == "__main__":
    main()
